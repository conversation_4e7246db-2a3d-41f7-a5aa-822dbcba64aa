# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
# 
#     http://www.apache.org/licenses/LICENSE-2.0
# 
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


#
# Providers
# See also: J2SE doc. "How to Implement a Provider for the JavaTM Cryptography Architecture"
#
# Android's provider of OpenSSL backed implementations
security.provider.1=com.android.org.conscrypt.OpenSSLProvider
# Android's version of the CertPathValidator and CertPathBuilder
security.provider.2=sun.security.provider.CertPathProvider
# Android's stripped down BouncyCastle provider
security.provider.3=com.android.org.bouncycastle.jce.provider.BouncyCastleProvider
# Android's provider of OpenSSL backed implementations
security.provider.4=com.android.org.conscrypt.JSSEProvider







# The default SSLSocketFactory and SSLServerSocketFactory provider implementations.
# See specification for 
# javax/net/ssl/SSLSocketFactory.html#getDefault()
# javax/net/ssl/SSLServerSocketFactory.html#getDefault()

# For regular SSLSockets, we have two implementations:
ssl.SocketFactory.provider=com.android.org.conscrypt.OpenSSLSocketFactoryImpl
#ssl.SocketFactory.provider=com.android.org.conscrypt.SSLSocketFactoryImpl

# For SSLServerSockets, there's only the new, OpenSSL-based implementation:
ssl.ServerSocketFactory.provider=com.android.org.conscrypt.OpenSSLSocketFactoryImpl

# Default KeyStore type.
# See specification for java/security/KeyStore.html#getDefaultType()
keystore.type=BKS


# Default KeyManagerFactory and TrustManagerFactory algorithms. 
# See specification for
# javax/net/ssl/KeyManagerFactory.html#getDefaultAlgorithm()
# javax/net/ssl/TrustManagerFactory.html#getDefaultAlgorithm()
ssl.KeyManagerFactory.algorithm=PKIX
ssl.TrustManagerFactory.algorithm=PKIX

# The following Android-only property controls peer certificate validation.
ssl.disablePeerCertificateChainVerification=false

# Disable weak algorithms in CertPathVerifier and CertPathBuilder.
jdk.certpath.disabledAlgorithms=MD2, MD4, RSA keySize < 1024, DSA keySize < 1024, EC keySize < 160

securerandom.strongAlgorithms=SHA1PRNG:AndroidOpenSSL
