# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
# 
#     http://www.apache.org/licenses/LICENSE-2.0
# 
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

#------------------------------------------------------------------------------
# Default logging property file.
# This file is used by java.util.logging package as default settings, users can
# specify another file instead with java.util.logging.config.file system 
# property, this property can be set via the Preference API, or as VM arguments 
# passed to "java" command, or as property definition passed to JNI_CreateJavaVM. 
# You can refer to JavaDoc of java.util.logging package for more information
# about this file.
#------------------------------------------------------------------------------

#------------------------------------------------------------------------------
# Global settings
#------------------------------------------------------------------------------

# Specify default level for global logger, the event whose level is below won't
# be logged. You can specify level for every logger, otherwise the level of parent 
# logger will be used. You can also set the level for every handler, as below for 
# java.util.logging.ConsoleHandler.
.level=INFO

# Specify handler classes list, these classes will be instantiated during the 
# logging framework initialization. The list should be white space separated.
# For example, use the line below to add SocketHandler. Note that the handler
# classes must be in the classpath.
#
# handlers=java.util.logging.ConsoleHandler java.util.logging.SocketHandler
#
handlers=java.util.logging.ConsoleHandler

# Specify a class names list, these classes' default constructor will be executed 
# during logging package initialization, which may contain some code to set the 
# logging configuration. The list should be white space separated, and the 
# classes must be in the classpath.
#
# config=


#------------------------------------------------------------------------------
# Handler settings
#------------------------------------------------------------------------------

# The properties below are samples for handler settings.
#java.util.logging.ConsoleHandler.formatter=java.util.logging.SimpleFormatter
#java.util.logging.ConsoleHandler.level=INFO
#java.util.logging.FileHandler.limit=100000
#java.util.logging.FileHandler.count=1
#java.util.logging.FileHandler.formatter=java.util.logging.XMLFormatter
#java.util.logging.FileHandler.pattern=%h/java%u.log
 
