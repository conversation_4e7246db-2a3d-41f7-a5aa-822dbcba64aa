<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="Highest">最高</string>
    <string name="SUV">SUV模式</string>
    <string name="abbrev_wday_month_day_no_year">EEEEMMMMd</string>
    <string name="abbrev_wday_month_day_no_year_alarm">EEEMMMd</string>
    <string name="abc_action_bar_home_description">Navigate home</string>
    <string name="abc_action_bar_up_description">Navigate up</string>
    <string name="abc_action_menu_overflow_description">More options</string>
    <string name="abc_action_mode_done">Done</string>
    <string name="abc_activity_chooser_view_see_all">See all</string>
    <string name="abc_activitychooserview_choose_application">Choose an app</string>
    <string name="abc_capital_off">OFF</string>
    <string name="abc_capital_on">ON</string>
    <string name="abc_menu_alt_shortcut_label">Alt+</string>
    <string name="abc_menu_ctrl_shortcut_label">Ctrl+</string>
    <string name="abc_menu_delete_shortcut_label">delete</string>
    <string name="abc_menu_enter_shortcut_label">enter</string>
    <string name="abc_menu_function_shortcut_label">Function+</string>
    <string name="abc_menu_meta_shortcut_label">Meta+</string>
    <string name="abc_menu_shift_shortcut_label">Shift+</string>
    <string name="abc_menu_space_shortcut_label">space</string>
    <string name="abc_menu_sym_shortcut_label">Sym+</string>
    <string name="abc_prepend_shortcut_label">Menu+</string>
    <string name="abc_search_hint">Search…</string>
    <string name="abc_searchview_description_clear">Clear query</string>
    <string name="abc_searchview_description_query">Search query</string>
    <string name="abc_searchview_description_search">Search</string>
    <string name="abc_searchview_description_submit">Submit query</string>
    <string name="abc_searchview_description_voice">Voice search</string>
    <string name="abc_shareactionprovider_share_with">Share with</string>
    <string name="abc_shareactionprovider_share_with_application">Share with %s</string>
    <string name="abc_slice_error">Couldn\'t connect</string>
    <string name="abc_slice_more">More</string>
    <string name="abc_slice_more_content">+ %1$d</string>
    <string name="abc_slice_permission_allow">Allow</string>
    <string name="abc_slice_permission_checkbox">Allow %1$s to show slices from any app</string>
    <string name="abc_slice_permission_deny">Deny</string>
    <string name="abc_slice_permission_text_1">- It can read information from %1$s</string>
    <string name="abc_slice_permission_text_2">- It can take actions inside %1$s</string>
    <string name="abc_slice_permission_title">Allow %1$s to show %2$s slices?</string>
    <string name="abc_slice_show_more">Show more</string>
    <string name="abc_slice_updated">Updated %1$s</string>
    <string name="abc_slices_permission_request">%1$s wants to show %2$s slices</string>
    <string name="abc_toolbar_collapse_description">Collapse</string>
    <string name="accessibility_accessibility_button">Accessibility</string>
    <string name="accessibility_action_divider_bottom_full">Bottom full screen</string>
    <string name="accessibility_action_divider_left_30">Left 30%</string>
    <string name="accessibility_action_divider_left_50">Left 50%</string>
    <string name="accessibility_action_divider_left_70">Left 70%</string>
    <string name="accessibility_action_divider_left_full">Left full screen</string>
    <string name="accessibility_action_divider_right_full">Right full screen</string>
    <string name="accessibility_action_divider_top_30">Top 30%</string>
    <string name="accessibility_action_divider_top_50">Top 50%</string>
    <string name="accessibility_action_divider_top_70">Top 70%</string>
    <string name="accessibility_action_divider_top_full">Top full screen</string>
    <string name="accessibility_action_pip_resize">Resize</string>
    <string name="accessibility_airplane_mode">Airplane mode.</string>
    <string name="accessibility_ambient_display_charging">Charging</string>
    <string name="accessibility_back">Back</string>
    <string name="accessibility_battery_details">Open battery details</string>
    <string name="accessibility_battery_full">Battery full.</string>
    <string name="accessibility_battery_level">Battery %d percent.</string>
    <string name="accessibility_battery_level_charging">Battery charging, %d percent.</string>
    <string name="accessibility_battery_level_with_estimate">Battery %1$s percent, about %2$s left based on your usage</string>
    <string name="accessibility_battery_one_bar">Battery one bar.</string>
    <string name="accessibility_battery_three_bars">Battery three bars.</string>
    <string name="accessibility_battery_two_bars">Battery two bars.</string>
    <string name="accessibility_battery_unknown">Battery percentage unknown.</string>
    <string name="accessibility_bluetooth_connected">Bluetooth connected.</string>
    <string name="accessibility_bluetooth_disconnected">Bluetooth disconnected.</string>
    <string name="accessibility_bluetooth_name">Connected to %s.</string>
    <string name="accessibility_bluetooth_tether">Bluetooth tethering.</string>
    <string name="accessibility_brightness">Display brightness</string>
    <string name="accessibility_bubble_dismissed">Bubble dismissed.</string>
    <string name="accessibility_camera_button">Camera</string>
    <string name="accessibility_cast_name">Connected to %s.</string>
    <string name="accessibility_casting">@string/quick_settings_casting</string>
    <string name="accessibility_casting_turned_off">Screen casting stopped.</string>
    <string name="accessibility_cell_data">Mobile Data</string>
    <string name="accessibility_cell_data_on">Mobile Data On</string>
    <string name="accessibility_clear_all">Clear all notifications.</string>
    <string name="accessibility_compatibility_zoom_button">Compatibility zoom button.</string>
    <string name="accessibility_compatibility_zoom_example">Zoom smaller to larger screen.</string>
    <string name="accessibility_control_change_favorite">favorite</string>
    <string name="accessibility_control_change_unfavorite">unfavorite</string>
    <string name="accessibility_control_favorite">Favorited</string>
    <string name="accessibility_control_favorite_position">Favorited, position %d</string>
    <string name="accessibility_control_move">Move to position %d</string>
    <string name="accessibility_control_not_favorite">Unfavorited</string>
    <string name="accessibility_data_connection_wifi">Wi-Fi</string>
    <string name="accessibility_data_one_bar">Data one bar.</string>
    <string name="accessibility_data_saver_off">Data Saver is off</string>
    <string name="accessibility_data_saver_on">Data Saver is on</string>
    <string name="accessibility_data_signal_full">Data signal full.</string>
    <string name="accessibility_data_three_bars">Data three bars.</string>
    <string name="accessibility_data_two_bars">Data two bars.</string>
    <string name="accessibility_desc_close">Close</string>
    <string name="accessibility_desc_connected">Connected.</string>
    <string name="accessibility_desc_connecting">Connecting.</string>
    <string name="accessibility_desc_lock_screen">Lock screen.</string>
    <string name="accessibility_desc_notification_icon">%1$s notification: %2$s</string>
    <string name="accessibility_desc_notification_shade">Notification shade.</string>
    <string name="accessibility_desc_off">Off.</string>
    <string name="accessibility_desc_on">On.</string>
    <string name="accessibility_desc_quick_settings">Quick settings.</string>
    <string name="accessibility_desc_quick_settings_edit">Quick settings editor.</string>
    <string name="accessibility_desc_recent_apps">Overview.</string>
    <string name="accessibility_desc_settings">Settings</string>
    <string name="accessibility_desc_work_lock">Work lock screen</string>
    <string name="accessibility_display_daltonizer_preference_subtitle">Color correction allows you to adjust how colors are displayed on your device</string>
    <string name="accessibility_display_daltonizer_preference_title">Color correction</string>
    <string name="accessibility_divider">Split-screen divider</string>
    <string name="accessibility_ethernet_connected">Ethernet connected.</string>
    <string name="accessibility_ethernet_disconnected">Ethernet disconnected.</string>
    <string name="accessibility_face_dialog_face_icon">Face icon</string>
    <string name="accessibility_fingerprint_dialog_fingerprint_icon">Fingerprint icon</string>
    <string name="accessibility_gps_acquiring">GPS acquiring.</string>
    <string name="accessibility_gps_enabled">GPS enabled.</string>
    <string name="accessibility_home">Home</string>
    <string name="accessibility_ime_switch_button">Switch input method</string>
    <string name="accessibility_key">Custom navigation button</string>
    <string name="accessibility_location_active">Location requests active</string>
    <string name="accessibility_long_click_tile">Open settings</string>
    <string name="accessibility_manage_notification">Manage notifications</string>
    <string name="accessibility_managed_profile">Work profile</string>
    <string name="accessibility_manual_zen_less_time">Less time.</string>
    <string name="accessibility_manual_zen_more_time">More time.</string>
    <string name="accessibility_menu">Menu</string>
    <string name="accessibility_multi_user_switch_inactive">Current user %s</string>
    <string name="accessibility_multi_user_switch_quick_contact">Show profile</string>
    <string name="accessibility_multi_user_switch_switcher">Switch user</string>
    <string name="accessibility_multi_user_switch_switcher_with_current">Switch user, current user %s</string>
    <string name="accessibility_no_battery">No battery.</string>
    <string name="accessibility_no_data">No data.</string>
    <string name="accessibility_no_phone">No phone.</string>
    <string name="accessibility_no_signal">No signal.</string>
    <string name="accessibility_no_sim">No SIM.</string>
    <string name="accessibility_no_sims">No SIM card.</string>
    <string name="accessibility_no_wifi">Wifi disconnected.</string>
    <string name="accessibility_no_wimax">No WiMAX.</string>
    <string name="accessibility_not_connected">Not connected.</string>
    <string name="accessibility_notification_dismissed">Notification dismissed.</string>
    <string name="accessibility_notification_section_header_gentle_clear_all">Clear all silent notifications</string>
    <string name="accessibility_notifications_button">Notifications.</string>
    <string name="accessibility_one_bar">One bar.</string>
    <string name="accessibility_output_chooser">Switch output device</string>
    <string name="accessibility_overflow_action">See all notifications</string>
    <string name="accessibility_phone_button">Phone</string>
    <string name="accessibility_phone_one_bar">Phone one bar.</string>
    <string name="accessibility_phone_signal_full">Phone signal full.</string>
    <string name="accessibility_phone_three_bars">Phone three bars.</string>
    <string name="accessibility_phone_two_bars">Phone two bars.</string>
    <string name="accessibility_qs_edit_position">Position %1$d</string>
    <string name="accessibility_qs_edit_remove_tile_action">remove tile</string>
    <string name="accessibility_qs_edit_tile_add_action">add tile to end</string>
    <string name="accessibility_qs_edit_tile_add_to_position">Add to position %1$d</string>
    <string name="accessibility_qs_edit_tile_move_to_position">Move to %1$d</string>
    <string name="accessibility_qs_edit_tile_start_add">Add tile</string>
    <string name="accessibility_qs_edit_tile_start_move">Move tile</string>
    <string name="accessibility_quick_settings_airplane_changed_off">Airplane mode turned off.</string>
    <string name="accessibility_quick_settings_airplane_changed_on">Airplane mode turned on.</string>
    <string name="accessibility_quick_settings_airplane_off">Airplane mode off.</string>
    <string name="accessibility_quick_settings_airplane_on">Airplane mode on.</string>
    <string name="accessibility_quick_settings_alarm">Alarm set for %s.</string>
    <string name="accessibility_quick_settings_alarm_set">Alarm set.</string>
    <string name="accessibility_quick_settings_battery">Battery %s.</string>
    <string name="accessibility_quick_settings_bluetooth">Bluetooth.</string>
    <string name="accessibility_quick_settings_bluetooth_changed_off">Bluetooth turned off.</string>
    <string name="accessibility_quick_settings_bluetooth_changed_on">Bluetooth turned on.</string>
    <string name="accessibility_quick_settings_bluetooth_connected">Bluetooth connected.</string>
    <string name="accessibility_quick_settings_bluetooth_connecting">Bluetooth connecting.</string>
    <string name="accessibility_quick_settings_bluetooth_off">Bluetooth off.</string>
    <string name="accessibility_quick_settings_bluetooth_on">Bluetooth on.</string>
    <string name="accessibility_quick_settings_close">Close panel.</string>
    <string name="accessibility_quick_settings_collapse">Close quick settings.</string>
    <string name="accessibility_quick_settings_color_inversion_changed_off">Color inversion turned off.</string>
    <string name="accessibility_quick_settings_color_inversion_changed_on">Color inversion turned on.</string>
    <string name="accessibility_quick_settings_data_saver_changed_off">Data Saver turned off.</string>
    <string name="accessibility_quick_settings_data_saver_changed_on">Data Saver turned on.</string>
    <string name="accessibility_quick_settings_detail">Quick Settings, %s.</string>
    <string name="accessibility_quick_settings_dnd">Do Not Disturb.</string>
    <string name="accessibility_quick_settings_dnd_alarms_on">alarms only</string>
    <string name="accessibility_quick_settings_dnd_changed_off">Do Not Disturb turned off.</string>
    <string name="accessibility_quick_settings_dnd_changed_on">Do Not Disturb turned on.</string>
    <string name="accessibility_quick_settings_dnd_none_on">total silence</string>
    <string name="accessibility_quick_settings_edit">Edit order of settings.</string>
    <string name="accessibility_quick_settings_expand">Open quick settings.</string>
    <string name="accessibility_quick_settings_flashlight_changed_off">Flashlight turned off.</string>
    <string name="accessibility_quick_settings_flashlight_changed_on">Flashlight turned on.</string>
    <string name="accessibility_quick_settings_flashlight_off">Flashlight off.</string>
    <string name="accessibility_quick_settings_flashlight_on">Flashlight on.</string>
    <string name="accessibility_quick_settings_flashlight_unavailable">Flashlight unavailable.</string>
    <string name="accessibility_quick_settings_hotspot_changed_off">Mobile hotspot turned off.</string>
    <string name="accessibility_quick_settings_hotspot_changed_on">Mobile hotspot turned on.</string>
    <string name="accessibility_quick_settings_less_time">Less time.</string>
    <string name="accessibility_quick_settings_location_changed_off">Location reporting turned off.</string>
    <string name="accessibility_quick_settings_location_changed_on">Location reporting turned on.</string>
    <string name="accessibility_quick_settings_location_off">Location reporting off.</string>
    <string name="accessibility_quick_settings_location_on">Location reporting on.</string>
    <string name="accessibility_quick_settings_mobile">Mobile %1$s. %2$s. %3$s.</string>
    <string name="accessibility_quick_settings_more_time">More time.</string>
    <string name="accessibility_quick_settings_not_available">Unvailable due to %s</string>
    <string name="accessibility_quick_settings_open_details">Open details.</string>
    <string name="accessibility_quick_settings_open_settings">Open %s settings.</string>
    <string name="accessibility_quick_settings_page">Page %1$d of %2$d</string>
    <string name="accessibility_quick_settings_rotation">Auto-rotate screen</string>
    <string name="accessibility_quick_settings_rotation_value">%s mode</string>
    <string name="accessibility_quick_settings_sensor_privacy_changed_off">Sensor Privacy turned off.</string>
    <string name="accessibility_quick_settings_sensor_privacy_changed_on">Sensor Privacy turned on.</string>
    <string name="accessibility_quick_settings_settings">Open settings.</string>
    <string name="accessibility_quick_settings_user">Signed in as %s</string>
    <string name="accessibility_quick_settings_wifi">%1$s.</string>
    <string name="accessibility_quick_settings_wifi_changed_off">Wifi turned off.</string>
    <string name="accessibility_quick_settings_wifi_changed_on">Wifi turned on.</string>
    <string name="accessibility_quick_settings_work_mode_changed_off">Work mode turned off.</string>
    <string name="accessibility_quick_settings_work_mode_changed_on">Work mode turned on.</string>
    <string name="accessibility_quick_settings_work_mode_off">Work mode off.</string>
    <string name="accessibility_quick_settings_work_mode_on">Work mode on.</string>
    <string name="accessibility_recent">Overview</string>
    <string name="accessibility_remove_notification">Clear notification.</string>
    <string name="accessibility_ringer_silent">Ringer silent.</string>
    <string name="accessibility_ringer_vibrate">Ringer vibrate.</string>
    <string name="accessibility_rotate_button">Rotate screen</string>
    <string name="accessibility_rotation_lock_off">Screen will rotate automatically.</string>
    <string name="accessibility_rotation_lock_off_changed">Screen will now rotate automatically.</string>
    <string name="accessibility_rotation_lock_on_landscape">Screen is locked in landscape orientation.</string>
    <string name="accessibility_rotation_lock_on_landscape_changed">Screen is now locked in landscape orientation.</string>
    <string name="accessibility_rotation_lock_on_portrait">Screen is locked in portrait orientation.</string>
    <string name="accessibility_rotation_lock_on_portrait_changed">Screen is now locked in portrait orientation.</string>
    <string name="accessibility_scanning_face">Scanning face</string>
    <string name="accessibility_search_light">Search</string>
    <string name="accessibility_send_smart_reply">Send</string>
    <string name="accessibility_sensors_off_active">Sensors off active</string>
    <string name="accessibility_settings_button">System settings.</string>
    <string name="accessibility_signal_full">Signal full.</string>
    <string name="accessibility_status_bar_headphones">Headphones connected</string>
    <string name="accessibility_status_bar_headset">Headset connected</string>
    <string name="accessibility_status_bar_hotspot">Hotspot</string>
    <string name="accessibility_three_bars">Three bars.</string>
    <string name="accessibility_tty_enabled">TeleTypewriter enabled.</string>
    <string name="accessibility_two_bars">Two bars.</string>
    <string name="accessibility_unlock_button">Unlock</string>
    <string name="accessibility_unlock_without_fingerprint">Unlock without using your fingerprint</string>
    <string name="accessibility_voice_assist_button">Voice Assist</string>
    <string name="accessibility_volume_close_odi_captions_tip">Close captions tip</string>
    <string name="accessibility_volume_collapse">Collapse</string>
    <string name="accessibility_volume_expand">Expand</string>
    <string name="accessibility_volume_settings">Sound settings</string>
    <string name="accessibility_vpn_on">VPN on.</string>
    <string name="accessibility_waiting_for_fingerprint">Waiting for fingerprint</string>
    <string name="accessibility_wifi_name">Connected to %s.</string>
    <string name="accessibility_wifi_off">Wifi off.</string>
    <string name="accessibility_wifi_one_bar">Wifi one bar.</string>
    <string name="accessibility_wifi_security_type_none">Open network</string>
    <string name="accessibility_wifi_security_type_secured">Secure network</string>
    <string name="accessibility_wifi_signal_full">Wifi signal full.</string>
    <string name="accessibility_wifi_three_bars">Wifi three bars.</string>
    <string name="accessibility_wifi_two_bars">Wifi two bars.</string>
    <string name="accessibility_wimax_one_bar">WiMAX one bar.</string>
    <string name="accessibility_wimax_signal_full">WiMAX signal full.</string>
    <string name="accessibility_wimax_three_bars">WiMAX three bars.</string>
    <string name="accessibility_wimax_two_bars">WiMAX two bars.</string>
    <string name="accessibility_work_mode">@string/quick_settings_work_mode_label</string>
    <string name="accessibility_work_profile_app_description">Work %s</string>
    <string name="accessibility_zero_bars">Zero bars.</string>
    <string name="accessor_expires_text">Lease expires at %s</string>
    <string name="accessor_info_title">Apps sharing data</string>
    <string name="accessor_no_description_text">No description provided by the app.</string>
    <string name="active_input_method_subtypes">Active input methods</string>
    <string name="activity_launch_on_secondary_display_failed_text">App does not support launch on secondary displays.</string>
    <string name="activity_not_found">Application is not installed on your device</string>
    <string name="adb_device_fingerprint_title_format">Device fingerprint: %1$s</string>
    <string name="adb_device_forget">Forget</string>
    <string name="adb_keys_warning_message">Revoke access to USB debugging from all computers you’ve previously authorized?</string>
    <string name="adb_pair_method_code_summary">Pair new devices using six digit code</string>
    <string name="adb_pair_method_code_title">Pair device with pairing code</string>
    <string name="adb_pair_method_qrcode_summary">Pair new devices using QR code scanner</string>
    <string name="adb_pair_method_qrcode_title">Pair device with QR code</string>
    <string name="adb_paired_devices_title">Paired devices</string>
    <string name="adb_pairing_device_dialog_failed_msg">Make sure the device is connected to the same network.</string>
    <string name="adb_pairing_device_dialog_failed_title">Pairing unsuccessful</string>
    <string name="adb_pairing_device_dialog_pairing_code_label">Wi‑Fi pairing code</string>
    <string name="adb_pairing_device_dialog_title">Pair with device</string>
    <string name="adb_qrcode_pairing_device_failed_msg">Failed to pair the device. Either the QR code was incorrect, or the device is not connected to the same network.</string>
    <string name="adb_warning_message">USB debugging is intended for development purposes only. Use it to copy data between your computer and your device, install apps on your device without notification, and read log data.</string>
    <string name="adb_warning_title">Allow USB debugging?</string>
    <string name="adb_wireless_connection_failed_message">Make sure %1$s is connected to the correct network</string>
    <string name="adb_wireless_connection_failed_title">Connection unsuccessful</string>
    <string name="adb_wireless_device_connected_summary">Currently connected</string>
    <string name="adb_wireless_device_details_title">Device details</string>
    <string name="adb_wireless_error">Error</string>
    <string name="adb_wireless_ip_addr_preference_title">IP address &amp; Port</string>
    <string name="adb_wireless_list_empty_off">To see and use available devices, turn on wireless debugging</string>
    <string name="adb_wireless_no_network_msg">Please connect to a Wi‑Fi network</string>
    <string name="adb_wireless_qrcode_pairing_description">Pair device over Wi‑Fi by scanning a QR code</string>
    <string name="adb_wireless_qrcode_pairing_title">Scan QR code</string>
    <string name="adb_wireless_qrcode_summary">Pair device over Wi‑Fi by scanning a QR code</string>
    <string name="adb_wireless_settings">Wireless debugging</string>
    <string name="adb_wireless_verifying_qrcode_text">Pairing device…</string>
    <string name="adbwifi_warning_message">Wireless debugging is intended for development purposes only. Use it to copy data between your computer and your device, install apps on your device without notification, and read log data.</string>
    <string name="adbwifi_warning_title">Allow wireless debugging?</string>
    <string name="add_tile">Add tile</string>
    <string name="adjust_button_width">Adjust button width</string>
    <string name="aeb_unavailable">紧急制动暂不可用</string>
    <string name="air_energy_conservation">节能模式</string>
    <string name="air_leakage_l_e">左后轮漏气,请安全停车,联系客服</string>
    <string name="air_leakage_l_s">左前轮漏气,请安全停车,联系客服</string>
    <string name="air_leakage_r_e">右后轮漏气,请安全停车,联系客服</string>
    <string name="air_leakage_r_s">右前轮漏气,请安全停车,联系客服</string>
    <string name="airbag_system_error">安全气囊系统异常,请尽快检修</string>
    <string name="airplane_mode">Airplane mode</string>
    <string name="alarm_template">at %1$s</string>
    <string name="alarm_template_far">on %1$s</string>
    <string name="all_app">全部应用</string>
    <string name="allow_mock_location">Allow mock locations</string>
    <string name="allow_mock_location_summary">Allow mock locations</string>
    <string name="always_use_accessory">Always open %1$s when %2$s is connected</string>
    <string name="always_use_device">Always open %1$s when %2$s is connected</string>
    <string name="ambient_light">氛围灯</string>
    <string name="ambient_light_individuation">个性化</string>
    <string name="ambient_light_music">音乐律动</string>
    <string name="androidx_startup">androidx.startup</string>
    <string name="animator_duration_scale_title">Animator duration scale</string>
    <string name="apn_settings_not_available">Access Point Name settings are not available for this user</string>
    <string name="app_accessed_mic">%1$s accessed your microphone</string>
    <string name="app_info">App info</string>
    <string name="app_label">System UI</string>
    <string name="app_name">Keyguard</string>
    <string name="app_process_limit_title">Background process limit</string>
    <string name="appbar_scrolling_view_behavior">com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior</string>
    <string name="appops_camera">This app is using the camera.</string>
    <string name="appops_camera_mic">This app is using the microphone and camera.</string>
    <string name="appops_camera_mic_overlay">This app is displaying over other apps on your screen and using the microphone and camera.</string>
    <string name="appops_camera_overlay">This app is displaying over other apps on your screen and using the camera.</string>
    <string name="appops_mic_overlay">This app is displaying over other apps on your screen and using the microphone.</string>
    <string name="appops_microphone">This app is using the microphone.</string>
    <string name="appops_overlay">This app is displaying over other apps on your screen.</string>
    <string name="auto">AUTO</string>
    <string name="auto_connect_disable">Auto-connect is off</string>
    <string name="auto_saver_enabled_text">Battery Saver will turn on automatically once battery goes below %d%%.</string>
    <string name="auto_saver_enabled_title">Battery Saver schedule turned on</string>
    <string name="auto_saver_okay_action">Got it</string>
    <string name="auto_saver_text">Turn on when battery is likely to run out</string>
    <string name="auto_saver_title">Tap to schedule Battery Saver</string>
    <string name="automatic">自动</string>
    <string name="available_via_app">Available via %1$s</string>
    <string name="available_via_carrier">Available via %1$s</string>
    <string name="available_via_passpoint">Available via %1$s</string>
    <string name="back_have_person_no_enter_zero">后排有人无法进入零重力</string>
    <string name="back_mot_win">隔断玻璃</string>
    <string name="back_ware_house_check">请确认后背门和后穹顶周围是否有障碍物</string>
    <string name="back_ware_house_pickup_check">拓展模式将开启后背窗和后穹顶，并关闭后背门和隔断玻璃，请确认后背门和后穹顶周围是否有障碍物</string>
    <string name="back_ware_window_check">行车时打开后穹顶有物品掉落风险,是否开启后穹顶？</string>
    <string name="back_window">后背窗</string>
    <string name="battery">Battery</string>
    <string name="battery_detail_charging_summary">Battery Saver not available during charging</string>
    <string name="battery_detail_switch_summary">Reduces performance and background data</string>
    <string name="battery_detail_switch_title">Battery Saver</string>
    <string name="battery_info_status_charging">Charging</string>
    <string name="battery_info_status_charging_fast">Charging rapidly</string>
    <string name="battery_info_status_charging_slow">Charging slowly</string>
    <string name="battery_info_status_discharging">Not charging</string>
    <string name="battery_info_status_full">Full</string>
    <string name="battery_info_status_not_charging">Plugged in, can\'t charge right now</string>
    <string name="battery_info_status_unknown">Unknown</string>
    <string name="battery_low_percent_format">%s remaining</string>
    <string name="battery_low_percent_format_hybrid">%1$s remaining, about %2$s left based on your usage</string>
    <string name="battery_low_percent_format_hybrid_short">%1$s remaining, about %2$s left</string>
    <string name="battery_low_percent_format_saver_started">%s remaining. Battery Saver is on.</string>
    <string name="battery_low_title">Battery may run out soon</string>
    <string name="battery_low_why">Settings</string>
    <string name="battery_meter_very_low_overlay_symbol">!</string>
    <string name="battery_panel_title">Battery usage</string>
    <string name="battery_saver_confirmation_ok">Turn on</string>
    <string name="battery_saver_confirmation_title">Turn on Battery Saver?</string>
    <string name="battery_saver_confirmation_title_generic">About Battery Saver</string>
    <string name="battery_saver_notification_action_text">Turn off Battery Saver</string>
    <string name="battery_saver_notification_text">Reduces performance and background data</string>
    <string name="battery_saver_notification_title">Battery Saver is on</string>
    <string name="battery_saver_start_action">Turn on Battery Saver</string>
    <string name="battery_state_unknown_notification_text">Tap for more information</string>
    <string name="battery_state_unknown_notification_title">Problem reading your battery meter</string>
    <string name="battry">电量</string>
    <string name="biometric_dialog_authenticated">Authenticated</string>
    <string name="biometric_dialog_confirm">Confirm</string>
    <string name="biometric_dialog_credential_attempts_before_wipe">Try again. Attempt %1$d of %2$d.</string>
    <string name="biometric_dialog_credential_too_many_attempts">Too many incorrect attempts.\nTry again in %d seconds.</string>
    <string name="biometric_dialog_empty_space_description">Tap to cancel authentication</string>
    <string name="biometric_dialog_face_icon_description_authenticated">Face authenticated</string>
    <string name="biometric_dialog_face_icon_description_authenticating">Looking for your face</string>
    <string name="biometric_dialog_face_icon_description_confirmed">Confirmed</string>
    <string name="biometric_dialog_face_icon_description_idle">Please try again</string>
    <string name="biometric_dialog_failed_attempts_now_wiping_device">Too many incorrect attempts. This device’s data will be deleted.</string>
    <string name="biometric_dialog_failed_attempts_now_wiping_profile">Too many incorrect attempts. This work profile and its data will be deleted.</string>
    <string name="biometric_dialog_failed_attempts_now_wiping_user">Too many incorrect attempts. This user will be deleted.</string>
    <string name="biometric_dialog_last_attempt_before_wipe_dialog_title">Your data will be deleted</string>
    <string name="biometric_dialog_last_password_attempt_before_wipe_device">If you enter an incorrect password on the next attempt, this device’s data will be deleted.</string>
    <string name="biometric_dialog_last_password_attempt_before_wipe_profile">If you enter an incorrect password on the next attempt, your work profile and its data will be deleted.</string>
    <string name="biometric_dialog_last_password_attempt_before_wipe_user">If you enter an incorrect password on the next attempt, this user will be deleted.</string>
    <string name="biometric_dialog_last_pattern_attempt_before_wipe_device">If you enter an incorrect pattern on the next attempt, this device’s data will be deleted.</string>
    <string name="biometric_dialog_last_pattern_attempt_before_wipe_profile">If you enter an incorrect pattern on the next attempt, your work profile and its data will be deleted.</string>
    <string name="biometric_dialog_last_pattern_attempt_before_wipe_user">If you enter an incorrect pattern on the next attempt, this user will be deleted.</string>
    <string name="biometric_dialog_last_pin_attempt_before_wipe_device">If you enter an incorrect PIN on the next attempt, this device’s data will be deleted.</string>
    <string name="biometric_dialog_last_pin_attempt_before_wipe_profile">If you enter an incorrect PIN on the next attempt, your work profile and its data will be deleted.</string>
    <string name="biometric_dialog_last_pin_attempt_before_wipe_user">If you enter an incorrect PIN on the next attempt, this user will be deleted.</string>
    <string name="biometric_dialog_now_wiping_dialog_dismiss">Dismiss</string>
    <string name="biometric_dialog_tap_confirm">Tap Confirm to complete</string>
    <string name="biometric_dialog_try_again">Try again</string>
    <string name="biometric_dialog_use_password">Use password</string>
    <string name="biometric_dialog_use_pattern">Use pattern</string>
    <string name="biometric_dialog_use_pin">Use PIN</string>
    <string name="biometric_dialog_wrong_password">Wrong password</string>
    <string name="biometric_dialog_wrong_pattern">Wrong pattern</string>
    <string name="biometric_dialog_wrong_pin">Wrong PIN</string>
    <string name="blob_expires_text">Expires at %s</string>
    <string name="blob_id_text">Shared data ID: %d</string>
    <string name="block">Block all notifications</string>
    <string name="bluetooth_a2dp_profile_summary_connected">Connected to media audio</string>
    <string name="bluetooth_a2dp_profile_summary_use_for">Use for media audio</string>
    <string name="bluetooth_active_battery_level">Active, %1$s battery</string>
    <string name="bluetooth_active_battery_level_untethered">Active, L: %1$s battery, R: %2$s battery</string>
    <string name="bluetooth_active_no_battery_level">Active</string>
    <string name="bluetooth_battery_level">%1$s battery</string>
    <string name="bluetooth_battery_level_untethered">L: %1$s battery, R: %2$s battery</string>
    <string name="bluetooth_connect_fail">连接失败，请重试</string>
    <string name="bluetooth_connect_success">蓝牙连接成功</string>
    <string name="bluetooth_connected">Connected%1$s</string>
    <string name="bluetooth_connected_battery_level">Connected, battery %1$s%2$s</string>
    <string name="bluetooth_connected_no_a2dp">Connected (no media)%1$s</string>
    <string name="bluetooth_connected_no_a2dp_battery_level">Connected (no media), battery %1$s%2$s</string>
    <string name="bluetooth_connected_no_headset">Connected (no phone)%1$s</string>
    <string name="bluetooth_connected_no_headset_battery_level">Connected (no phone), battery %1$s%2$s</string>
    <string name="bluetooth_connected_no_headset_no_a2dp">Connected (no phone or media)%1$s</string>
    <string name="bluetooth_connected_no_headset_no_a2dp_battery_level">Connected (no phone or media), battery %1$s%2$s</string>
    <string name="bluetooth_connected_no_map">Connected (no message access)%1$s</string>
    <string name="bluetooth_connecting">Connecting…</string>
    <string name="bluetooth_disable_absolute_volume">Disable absolute volume</string>
    <string name="bluetooth_disable_absolute_volume_summary">Disables the Bluetooth absolute volume feature in case of volume issues with remote devices such as unacceptably loud volume or lack of control.</string>
    <string name="bluetooth_disconnected">Disconnected</string>
    <string name="bluetooth_disconnecting">Disconnecting…</string>
    <string name="bluetooth_enable_gabeldorsche">Enable Gabeldorsche</string>
    <string name="bluetooth_enable_gabeldorsche_summary">Enables the Bluetooth Gabeldorsche feature stack.</string>
    <string name="bluetooth_equipment">蓝牙设备</string>
    <string name="bluetooth_headset_profile_summary_connected">Connected to phone audio</string>
    <string name="bluetooth_headset_profile_summary_use_for">Use for phone audio</string>
    <string name="bluetooth_hearing_aid_profile_summary_connected">Connected to Hearing Aids</string>
    <string name="bluetooth_hearing_aid_profile_summary_use_for">Use for Hearing Aids</string>
    <string name="bluetooth_hearingaid_left_battery_level">Left - %1$s battery</string>
    <string name="bluetooth_hearingaid_left_pairing_message">Pairing left hearing aid…</string>
    <string name="bluetooth_hearingaid_right_battery_level">Right - %1$s battery</string>
    <string name="bluetooth_hearingaid_right_pairing_message">Pairing right hearing aid…</string>
    <string name="bluetooth_hid_profile_summary_connected">Connected to input device</string>
    <string name="bluetooth_hid_profile_summary_use_for">Use for input</string>
    <string name="bluetooth_map_profile_summary_connected">Connected to map</string>
    <string name="bluetooth_map_profile_summary_use_for">Use for map</string>
    <string name="bluetooth_opp_profile_summary_connected">Connected to file transfer server</string>
    <string name="bluetooth_opp_profile_summary_not_connected">Not connected to file transfer server</string>
    <string name="bluetooth_opp_profile_summary_use_for">Use for file transfer</string>
    <string name="bluetooth_pairing">Pairing…</string>
    <string name="bluetooth_pairing_accept">Pair</string>
    <string name="bluetooth_pairing_accept_all_caps">PAIR</string>
    <string name="bluetooth_pairing_decline">Cancel</string>
    <string name="bluetooth_pairing_device_down_error_message">Can\'t communicate with %1$s.</string>
    <string name="bluetooth_pairing_error_message">Couldn\'t pair with %1$s.</string>
    <string name="bluetooth_pairing_pin_error_message">Couldn\'t pair with %1$s because of an incorrect PIN or passkey.</string>
    <string name="bluetooth_pairing_rejected_error_message">Pairing rejected by %1$s.</string>
    <string name="bluetooth_pairing_will_share_phonebook">Pairing grants access to your contacts and call history when connected.</string>
    <string name="bluetooth_pan_nap_profile_summary_connected">Sharing local internet connection with device</string>
    <string name="bluetooth_pan_profile_summary_use_for">Use for internet access</string>
    <string name="bluetooth_pan_user_profile_summary_connected">Connected to device for internet access</string>
    <string name="bluetooth_pari_fail">配对失败，请重试</string>
    <string name="bluetooth_profile_a2dp">Media audio</string>
    <string name="bluetooth_profile_a2dp_high_quality">HD audio: %1$s</string>
    <string name="bluetooth_profile_a2dp_high_quality_unknown_codec">HD audio</string>
    <string name="bluetooth_profile_headset">Phone calls</string>
    <string name="bluetooth_profile_hearing_aid">Hearing Aids</string>
    <string name="bluetooth_profile_hid">Input device</string>
    <string name="bluetooth_profile_map">Text Messages</string>
    <string name="bluetooth_profile_opp">File transfer</string>
    <string name="bluetooth_profile_pan">Internet access</string>
    <string name="bluetooth_profile_pan_nap">Internet connection sharing</string>
    <string name="bluetooth_profile_pbap">Contact sharing</string>
    <string name="bluetooth_profile_pbap_summary">Use for contact sharing</string>
    <string name="bluetooth_profile_sap">SIM Access</string>
    <string name="bluetooth_sap_profile_summary_connected">Connected to SAP</string>
    <string name="bluetooth_sap_profile_summary_use_for">Use for SIM access</string>
    <string name="bluetooth_select_a2dp_codec_bits_per_sample">Bluetooth Audio Bits Per Sample</string>
    <string name="bluetooth_select_a2dp_codec_bits_per_sample_dialog_title">Trigger Bluetooth Audio Codec\nSelection: Bits Per Sample</string>
    <string name="bluetooth_select_a2dp_codec_channel_mode">Bluetooth Audio Channel Mode</string>
    <string name="bluetooth_select_a2dp_codec_channel_mode_dialog_title">Trigger Bluetooth Audio Codec\nSelection: Channel Mode</string>
    <string name="bluetooth_select_a2dp_codec_ldac_playback_quality">Bluetooth Audio LDAC Codec: Playback Quality</string>
    <string name="bluetooth_select_a2dp_codec_ldac_playback_quality_dialog_title">Trigger Bluetooth Audio LDAC\nCodec Selection: Playback Quality</string>
    <string name="bluetooth_select_a2dp_codec_sample_rate">Bluetooth Audio Sample Rate</string>
    <string name="bluetooth_select_a2dp_codec_sample_rate_dialog_title">Trigger Bluetooth Audio Codec\nSelection: Sample Rate</string>
    <string name="bluetooth_select_a2dp_codec_streaming_label">Streaming: %1$s</string>
    <string name="bluetooth_select_a2dp_codec_type">Bluetooth Audio Codec</string>
    <string name="bluetooth_select_a2dp_codec_type_dialog_title">Trigger Bluetooth Audio Codec\nSelection</string>
    <string name="bluetooth_select_a2dp_codec_type_help_info">Gray-out means not supported by phone or headset</string>
    <string name="bluetooth_select_avrcp_version_dialog_title">Select Bluetooth AVRCP Version</string>
    <string name="bluetooth_select_avrcp_version_string">Bluetooth AVRCP Version</string>
    <string name="bluetooth_select_map_version_dialog_title">Select Bluetooth MAP Version</string>
    <string name="bluetooth_select_map_version_string">Bluetooth MAP Version</string>
    <string name="bluetooth_show_devices_without_names">Show Bluetooth devices without names</string>
    <string name="bluetooth_show_devices_without_names_summary">Bluetooth devices without names (MAC addresses only) will be displayed</string>
    <string name="bluetooth_talkback_bluetooth">Bluetooth</string>
    <string name="bluetooth_talkback_computer">Computer</string>
    <string name="bluetooth_talkback_headphone">Headphone</string>
    <string name="bluetooth_talkback_headset">Headset</string>
    <string name="bluetooth_talkback_imaging">Imaging</string>
    <string name="bluetooth_talkback_input_peripheral">Input Peripheral</string>
    <string name="bluetooth_talkback_phone">Phone</string>
    <string name="bluetooth_tethered">Bluetooth tethered</string>
    <string name="bottom_sheet_behavior">com.google.android.material.bottomsheet.BottomSheetBehavior</string>
    <string name="brake_pedal_press_mode">需踩下制动踏板以开启该模式</string>
    <string name="branded_monitoring_description_app_personal">You\'re connected to %1$s, which can monitor your personal network activity, including emails, apps, and websites.</string>
    <string name="branded_vpn_footer">Network may be monitored</string>
    <string name="broadcast_tile">Broadcast Tile</string>
    <string name="brvah_app_name">Library</string>
    <string name="brvah_load_end">没有更多数据</string>
    <string name="brvah_load_failed">加载失败，请点我重试</string>
    <string name="brvah_loading">正在加载中...</string>
    <string name="bt_hci_snoop_log">Enable Bluetooth HCI snoop log</string>
    <string name="bt_hci_snoop_log_summary">Capture Bluetooth packets. (Toggle Bluetooth after changing this setting)</string>
    <string name="bt_is_off">Bluetooth is off</string>
    <string name="btn_cancel">取消</string>
    <string name="btn_join">加入</string>
    <string name="btn_remove">移除</string>
    <string name="btn_rest">重置</string>
    <string name="btn_sure">确定</string>
    <string name="bubble_accessibility_action_add_back">Add back to stack</string>
    <string name="bubble_accessibility_action_move">Move</string>
    <string name="bubble_accessibility_action_move_bottom_left">Move bottom left</string>
    <string name="bubble_accessibility_action_move_bottom_right">Move bottom right</string>
    <string name="bubble_accessibility_action_move_top_left">Move top left</string>
    <string name="bubble_accessibility_action_move_top_right">Move top right</string>
    <string name="bubble_content_description_single">%1$s from %2$s</string>
    <string name="bubble_content_description_stack">%1$s from %2$s and %3$d more</string>
    <string name="bubble_dismiss_text">Dismiss bubble</string>
    <string name="bubble_overflow_button_content_description">Overflow</string>
    <string name="bubble_overflow_empty_subtitle">Recent bubbles and dismissed bubbles will appear here</string>
    <string name="bubble_overflow_empty_title">No recent bubbles</string>
    <string name="bubbles_app_settings">%1$s settings</string>
    <string name="bubbles_dont_bubble_conversation">Don’t bubble conversation</string>
    <string name="bubbles_settings_button_description">Settings for %1$s bubbles</string>
    <string name="bubbles_user_education_description">New conversations appear as floating icons, or bubbles. Tap to open bubble. Drag to move it.</string>
    <string name="bubbles_user_education_got_it">Got it</string>
    <string name="bubbles_user_education_manage">Tap Manage to turn off bubbles from this app</string>
    <string name="bubbles_user_education_manage_title">Control bubbles anytime</string>
    <string name="bubbles_user_education_title">Chat using bubbles</string>
    <string name="bugreport_in_power">Bug report shortcut</string>
    <string name="bugreport_in_power_summary">Show a button in the power menu for taking a bug report</string>
    <string name="button_convert_fbe">Wipe and convert…</string>
    <string name="cache_db_name">app_icons.db</string>
    <string name="cached_apps_freezer_device_default">Device default</string>
    <string name="cached_apps_freezer_disabled">Disabled</string>
    <string name="cached_apps_freezer_enabled">Enabled</string>
    <string name="cached_apps_freezer_reboot_dialog_text">Your device must be rebooted for this change to apply. Reboot now or cancel.</string>
    <string name="camera_hint">Swipe from icon for camera</string>
    <string name="camera_label">open camera</string>
    <string name="camera_permission">摄像头权限</string>
    <string name="camp">营地守护模式</string>
    <string name="camp2">营地守护</string>
    <string name="can_not_match">无法配对</string>
    <string name="cancel">Cancel</string>
    <string name="car_out_shout">车外广播</string>
    <string name="carrier_network_change_mode">Carrier network changing</string>
    <string name="category_personal">Personal</string>
    <string name="category_work">Work</string>
    <string name="caui_content_description_switch_off" formatted="false">[l1]%s|%s关闭|关闭%s</string>
    <string name="caui_content_description_switch_on" formatted="false">[l1]%s|%s打开|打开%s|开启%s</string>
    <string name="caui_default_text_cancel">取消</string>
    <string name="caui_default_text_network_exception">网络异常</string>
    <string name="caui_default_text_network_exception_desc">请检查网络设置，或稍后再试</string>
    <string name="caui_default_text_network_settings">网络设置</string>
    <string name="caui_default_text_retry">重试</string>
    <string name="caui_text_cancel">取消</string>
    <string name="caui_text_download_success">下载成功</string>
    <string name="caui_text_fail">下载失败 </string>
    <string name="caui_text_retry">重新获取</string>
    <string name="caui_text_timepicker_day">日</string>
    <string name="caui_text_timepicker_day_temp">日</string>
    <string name="caui_text_timepicker_end">结束时间</string>
    <string name="caui_text_timepicker_hour">时</string>
    <string name="caui_text_timepicker_minute">分</string>
    <string name="caui_text_timepicker_month">月</string>
    <string name="caui_text_timepicker_month_temp">月</string>
    <string name="caui_text_timepicker_second">秒</string>
    <string name="caui_text_timepicker_select_date">选择日期</string>
    <string name="caui_text_timepicker_select_time">选择时间</string>
    <string name="caui_text_timepicker_start">开始时间</string>
    <string name="caui_text_timepicker_year">年</string>
    <string name="caui_text_timepicker_year_temp">年</string>
    <string name="cell_data_off">Off</string>
    <string name="cell_data_off_content_description">Mobile data off</string>
    <string name="certinstaller_package">com.android.certinstaller</string>
    <string name="change_theme_reboot">Changing the theme requires a restart.</string>
    <string name="character_counter_content_description">Characters entered %1$d of %2$d</string>
    <string name="character_counter_overflowed_content_description">Character limit exceeded %1$d of %2$d</string>
    <string name="character_counter_pattern">%1$d/%2$d</string>
    <string name="charge_length_format">%1$s ago</string>
    <string name="charging_port">充电口</string>
    <string name="chip_text">Chip text</string>
    <string name="choange_gear_by_charge">为了您的安全,请拔出充电枪再换挡</string>
    <string name="choose_profile">Choose profile</string>
    <string name="clear_adb_keys">Revoke USB debugging authorizations</string>
    <string name="clear_all_notifications_text">Clear all</string>
    <string name="clear_memory_failed">内存清理失败</string>
    <string name="clear_memory_succeed">内存清理已完成</string>
    <string name="clear_text_end_icon_content_description">Clear text</string>
    <string name="click_to_add">点击添加到右侧</string>
    <string name="click_to_close">点击关闭</string>
    <string name="click_to_open">点击开启</string>
    <string name="click_to_pause">点击暂停</string>
    <string name="clipboard">Clipboard</string>
    <string name="clock">Clock</string>
    <string name="clock_12hr_format">hm</string>
    <string name="clock_24hr_format">Hm</string>
    <string name="clock_seconds">Show clock seconds</string>
    <string name="clock_seconds_desc">Show clock seconds in the status bar. May impact battery life.</string>
    <string name="clock_title_analog">Analog</string>
    <string name="clock_title_bubble">Bubble</string>
    <string name="clock_title_default">Default</string>
    <string name="closure">关闭</string>
    <string name="co_driver_back_have_person">副驾有人，后排尊享位置暂不可用</string>
    <string name="co_screen_control">副驾屏开关</string>
    <string name="co_screen_expansion">副驾屏展开</string>
    <string name="co_screen_fold">副驾屏折叠</string>
    <string name="cobt">副驾蓝牙</string>
    <string name="color_temp_standard">标准</string>
    <string name="compat_mode_off">Stretch to fill screen</string>
    <string name="compat_mode_on">Zoom to fill screen</string>
    <string name="config_batteryStateUnknownUrl" />
    <string name="config_cameraProtectionExcludedPackages" />
    <string name="config_frontBuiltInDisplayCutoutProtection" />
    <string name="config_navBarLayout">left[.5W],back[1WC];home;recent[1WC],right[.5W]</string>
    <string name="config_navBarLayoutHandle">back[40AC];home_handle;ime_switcher[40AC]</string>
    <string name="config_navBarLayoutQuickstep">back[1.7WC];home;contextual[1.7WC]</string>
    <string name="config_protectedCameraId" />
    <string name="config_recentsComponent">com.android.systemui.recents.OverviewProxyRecentsImpl</string>
    <string name="config_rounded_mask">M8,0C3.6,0,0,3.6,0,8</string>
    <string name="config_screenshotEditor" />
    <string name="config_statusBarComponent">com.android.systemui.statusbar.phone.StatusBar</string>
    <string name="config_systemUIFactoryComponent">com.android.systemui.SystemUIFactory</string>
    <string name="config_systemUIVendorServiceComponent">com.android.systemui.VendorServices</string>
    <string name="confirm_enable_oem_unlock_text">WARNING: Device protection features will not work on this device while this setting is turned on.</string>
    <string name="confirm_enable_oem_unlock_title">Allow OEM unlocking?</string>
    <string name="congress_music_mode_close">退出副驾</string>
    <string name="congress_music_mode_open">打开副驾</string>
    <string name="connected_via_app">Connected via %1$s</string>
    <string name="connected_via_carrier">Connected via %1$s</string>
    <string name="connected_via_network_scorer">Automatically connected via %1$s</string>
    <string name="connected_via_network_scorer_default">Automatically connected via network rating provider</string>
    <string name="connected_via_passpoint">Connected via %1$s</string>
    <string name="content_description_account">[切换|打开][我的](账户|账号)</string>
    <string name="content_description_bluetooth">蓝牙|蓝牙设置</string>
    <string name="content_description_bluetooth2">[l2]蓝牙</string>
    <string name="content_description_co_adjust_seat">座椅调节|打开座椅调节</string>
    <string name="content_description_co_apps">APPlist|全部应用</string>
    <string name="content_description_co_go_home">桌面|回到桌面</string>
    <string name="content_description_co_open_warn">打开报警信息|报警信息</string>
    <string name="content_description_co_screen_angle">展开角度|展开角度调节</string>
    <string name="content_description_co_setting">设置|快捷设置|副驾屏设置|打开设置</string>
    <string name="content_description_co_waln_name">修改热点名称</string>
    <string name="content_description_co_waln_passwarod">[修改连接密码]|[连接密码]</string>
    <string name="content_description_connect">[连接]%s</string>
    <string name="content_description_danger">[打开|查看](报警|警报|警告)[信息|消息]</string>
    <string name="content_description_disconnect">[断开]%s</string>
    <string name="content_description_menu_button">Menu</string>
    <string name="content_description_message">[打开|查看](报警|警报|警告|通知|消息)[信息|消息|中心]</string>
    <string name="content_description_net">4G|4G网络|车机网络|网络设置</string>
    <string name="content_description_net2">[l2]网络</string>
    <string name="content_description_notification">[打开|查看](通知|消息)[中心]</string>
    <string name="content_description_notification_clean">(清空|清除|删除)[通知|消息]</string>
    <string name="content_description_open_app">[打开]%s</string>
    <string name="content_description_recorder">[行车]记录仪</string>
    <string name="content_description_recorder_car_sound">车内声音</string>
    <string name="content_description_recorder_loop">视频录制</string>
    <string name="content_description_recorder_watermark">水印</string>
    <string name="content_description_refresh">刷新</string>
    <string name="content_description_scene">情景模式</string>
    <string name="content_description_switch_close" formatted="false">[l1]%s|%s关闭|关闭%s</string>
    <string name="content_description_switch_open" formatted="false">[l1]%s|%s打开|打开%s|开启%s</string>
    <string name="content_description_wifi">WLAN|WLAN设置|Wifi|Wifi设置</string>
    <string name="content_description_wifi2">[l2]WLAN</string>
    <string name="content_description_wifi_ap">热点|热点设置</string>
    <string name="content_description_wifi_ap2">[l2]热点</string>
    <string name="content_drag_card_add" formatted="false">添加&lt;%s&gt;|&lt;%s&gt;添加</string>
    <string name="content_drag_card_remove" formatted="false">删除&lt;%s&gt;|&lt;%s&gt;删除</string>
    <string name="content_drag_card_tab">{0}|{0}{1}|{1}{0}</string>
    <string name="content_drag_card_tab_close">{0}{1}|{1}{0}</string>
    <string name="content_drag_card_tab_wiper">雨刮{0}|雨刷{0}|{1}{0}</string>
    <string name="content_drag_card_tab_wiper_sensitivity">雨刮{0}|雨刷{0}|自动{0}</string>
    <string name="controls_added_tooltip">Hold Power button to see new controls</string>
    <string name="controls_confirmation_confirming">Confirming…</string>
    <string name="controls_confirmation_message">Confirm change for %s</string>
    <string name="controls_dialog_confirmation">Controls updated</string>
    <string name="controls_dialog_message">Suggested by %s</string>
    <string name="controls_dialog_ok">Add</string>
    <string name="controls_dialog_title">Add to device controls</string>
    <string name="controls_error_failed">Error, try again</string>
    <string name="controls_error_generic">Can’t load status</string>
    <string name="controls_error_removed">Not found</string>
    <string name="controls_error_removed_message">Couldn’t access %1$s. Check the %2$s app to make sure the control is still available and that the app settings haven’t changed.</string>
    <string name="controls_error_removed_title">Control is unavailable</string>
    <string name="controls_error_retryable">Error, retrying…</string>
    <string name="controls_error_timeout">Inactive, check app</string>
    <string name="controls_favorite_default_title">Controls</string>
    <string name="controls_favorite_load_error">Controls could not be loaded. Check the %s app to make sure that the app settings haven’t changed.</string>
    <string name="controls_favorite_load_none">Compatible controls unavailable</string>
    <string name="controls_favorite_other_zone_header">Other</string>
    <string name="controls_favorite_rearrange">Hold &amp; drag to rearrange controls</string>
    <string name="controls_favorite_removed">All controls removed</string>
    <string name="controls_favorite_see_other_apps">See other apps</string>
    <string name="controls_favorite_subtitle">Choose controls to access from the power menu</string>
    <string name="controls_favorite_toast_no_changes">Changes not saved</string>
    <string name="controls_in_progress">In progress</string>
    <string name="controls_media_active_session">Current session cannot be hidden.</string>
    <string name="controls_media_close_session">Hide the current session.</string>
    <string name="controls_media_dismiss_button">Dismiss</string>
    <string name="controls_media_resume">Resume</string>
    <string name="controls_media_settings_button">Settings</string>
    <string name="controls_media_title">Media</string>
    <string name="controls_menu_add">Add controls</string>
    <string name="controls_menu_edit">Edit controls</string>
    <string name="controls_open_app">Open app</string>
    <string name="controls_pin_instructions">Enter PIN</string>
    <string name="controls_pin_instructions_retry">Try another PIN</string>
    <string name="controls_pin_use_alphanumeric">PIN contains letters or symbols</string>
    <string name="controls_pin_verify">Verify %s</string>
    <string name="controls_pin_verifying">Verifying…</string>
    <string name="controls_pin_wrong">Wrong PIN</string>
    <string name="controls_providers_title">Choose app to add controls</string>
    <string name="controls_removed">Removed</string>
    <string name="controls_seeding_in_progress">Loading recommendations</string>
    <string name="controls_structure_tooltip">Swipe to see more</string>
    <string name="convert_to_fbe_warning">Convert data partition to file based encryption.\n !!Warning!! This will erase all your data.\n This feature is alpha, and may not work correctly.\n Press \'Wipe and convert…\' to continue.</string>
    <string name="convert_to_file_encryption">Convert to file encryption</string>
    <string name="convert_to_file_encryption_done">Already file encrypted</string>
    <string name="convert_to_file_encryption_enabled">Convert…</string>
    <string name="copy">Copy</string>
    <string name="copy_app_info">移除后在中控屏中仍保留</string>
    <string name="copy_app_title">是否确认从副驾屏中移除此应用？</string>
    <string name="custom_mode">自定义</string>
    <string name="d">D</string>
    <string name="daltonizer_mode_deuteranomaly">Deuteranomaly (red-green)</string>
    <string name="daltonizer_mode_disabled">Disabled</string>
    <string name="daltonizer_mode_monochromacy">Monochromacy</string>
    <string name="daltonizer_mode_protanomaly">Protanomaly (red-green)</string>
    <string name="daltonizer_mode_tritanomaly">Tritanomaly (blue-yellow)</string>
    <string name="daltonizer_type_overridden">Overridden by %1$s</string>
    <string name="danger_signal">报警消息</string>
    <string name="dark_night">深色</string>
    <string name="data_connection_3_5g">H</string>
    <string name="data_connection_3_5g_plus">H+</string>
    <string name="data_connection_3g">3G</string>
    <string name="data_connection_4g">4G</string>
    <string name="data_connection_4g_plus">4G+</string>
    <string name="data_connection_5g">5G</string>
    <string name="data_connection_5g_plus">5G+</string>
    <string name="data_connection_5ge">5Ge</string>
    <string name="data_connection_5ge_html">&lt;i&gt;5G &lt;small&gt;E&lt;/small&gt;&lt;/i&gt;</string>
    <string name="data_connection_cdma">1X</string>
    <string name="data_connection_edge">EDGE</string>
    <string name="data_connection_gprs">GPRS</string>
    <string name="data_connection_hspa">HSPA</string>
    <string name="data_connection_lte">LTE</string>
    <string name="data_connection_lte_plus">LTE+</string>
    <string name="data_connection_no_internet">No internet</string>
    <string name="data_connection_roaming">Roaming</string>
    <string name="data_saver">Data Saver</string>
    <string name="data_usage_disabled_dialog">The data limit you set has been reached. You are no longer using mobile data.\n\nIf you resume, charges may apply for data usage.</string>
    <string name="data_usage_disabled_dialog_3g_title">2G-3G data is paused</string>
    <string name="data_usage_disabled_dialog_4g_title">4G data is paused</string>
    <string name="data_usage_disabled_dialog_enable">Resume</string>
    <string name="data_usage_disabled_dialog_mobile_title">Mobile data is paused</string>
    <string name="data_usage_disabled_dialog_title">Data is paused</string>
    <string name="data_usage_ota">System updates</string>
    <string name="data_usage_uninstalled_apps">Removed apps</string>
    <string name="data_usage_uninstalled_apps_users">Removed apps and users</string>
    <string name="debug_app">Select debug app</string>
    <string name="debug_app_not_set">No debug application set</string>
    <string name="debug_app_set">Debugging application: %1$s</string>
    <string name="debug_applications_category">Apps</string>
    <string name="debug_debugging_category">Debugging</string>
    <string name="debug_drawing_category">Drawing</string>
    <string name="debug_hw_drawing_category">Hardware accelerated rendering</string>
    <string name="debug_hw_overdraw">Debug GPU overdraw</string>
    <string name="debug_input_category">Input</string>
    <string name="debug_layout">Show layout bounds</string>
    <string name="debug_layout_summary">Show clip bounds, margins, etc.</string>
    <string name="debug_monitoring_category">Monitoring</string>
    <string name="debug_networking_category">Networking</string>
    <string name="debug_view_attributes">Enable view attribute inspection</string>
    <string name="default_theme">Default</string>
    <string name="delete_blob_confirmation_text">Are you sure you want to delete this shared data?</string>
    <string name="delete_blob_text">Delete shared data</string>
    <string name="demo_mode">System UI demo mode</string>
    <string name="demote">Mark this notification as not a conversation</string>
    <string name="description_direction_left">Slide left for %s.</string>
    <string name="description_direction_up">Slide up for %s.</string>
    <string name="description_target_search">Search</string>
    <string name="dessert_case">Dessert Case</string>
    <string name="dev_logpersist_clear_warning_message">When we no longer are monitoring with the persistent logger, we are required to erase the logger data resident on your device.</string>
    <string name="dev_logpersist_clear_warning_title">Clear logger persistent storage?</string>
    <string name="dev_settings_warning_message">These settings are intended for development use only. They can cause your device and the applications on it to break or misbehave.</string>
    <string name="dev_settings_warning_title">Allow development settings?</string>
    <string name="development_settings_enable">Enable developer options</string>
    <string name="development_settings_not_available">Developer options are not available for this user</string>
    <string name="development_settings_summary">Set options for app development</string>
    <string name="development_settings_title">Developer options</string>
    <string name="device_services">Device Services</string>
    <string name="dial_plate_check">切换蓝牙</string>
    <string name="dial_plate_connect">当前已连接设备：</string>
    <string name="dial_plate_record">尚未同步通话记录</string>
    <string name="dial_plate_synchronize">立即同步</string>
    <string name="dialog_cancel">取消</string>
    <string name="dialog_confirm">确认</string>
    <string name="dialog_content">是否移除“蓝牙名称a”的配对记录？</string>
    <string name="dialog_title">确认删除配对记录</string>
    <string name="dialog_title_device_change_connect">切换蓝牙设备</string>
    <string name="direct_boot_unaware_dialog_message">Note: After a reboot, this app can\'t start until you unlock your phone</string>
    <string name="disable_carrier_button_text">Disable eSIM</string>
    <string name="disable_overlays">Disable HW overlays</string>
    <string name="disable_overlays_summary">Always use GPU for screen compositing</string>
    <string name="disable_vpn">Disable VPN</string>
    <string name="disabled">Disabled</string>
    <string name="disabled_by_admin">Disabled by admin</string>
    <string name="disabled_by_admin_summary_text">Controlled by admin</string>
    <string name="disconnect_bluetooth">断开设备</string>
    <string name="disconnect_vpn">Disconnect VPN</string>
    <string name="disconnect_wifi">断开WIFI</string>
    <string name="disconnect_wifiap">断开网络连接</string>
    <string name="dnd_is_off">Do Not Disturb is off</string>
    <string name="dnd_suppressing_shade_text">Notifications paused by Do Not Disturb</string>
    <string name="do_disclosure_generic">This device belongs to your organization</string>
    <string name="do_disclosure_with_name">This device belongs to %s</string>
    <string name="do_not_silence">Don\'t silence</string>
    <string name="do_not_silence_block">Don\'t silence or block</string>
    <string name="dock_alignment_not_charging">Realign phone to charge wirelessly</string>
    <string name="dock_alignment_slow_charging">Realign phone for faster charging</string>
    <string name="dock_app_edit_no_p">请在驻车时编辑桌面</string>
    <string name="dock_app_setting_air">控制</string>
    <string name="dock_app_setting_app">应用</string>
    <string name="dock_app_setting_litttle">小程序</string>
    <string name="dock_forced_resizable">App may not work with split-screen.</string>
    <string name="dock_has_app">工具栏中已有该应用</string>
    <string name="dock_non_resizeble_failed_to_dock_text">App does not support split-screen.</string>
    <string name="door_child_lock">儿童锁</string>
    <string name="doze_brightness_sensor_type" />
    <string name="drag_to_add_tiles">Hold and drag to add tiles</string>
    <string name="drag_to_rearrange_tiles">Hold and drag to rearrange tiles</string>
    <string name="drag_to_remove_disabled">You need at least %1$d tiles</string>
    <string name="drag_to_remove_tiles">Drag here to remove</string>
    <string name="driver_have_obstacle_remove">请确认后排无人或物品，且后排座椅为非折叠状态</string>
    <string name="driving_mode">驾驶模式</string>
    <string name="driving_no_open">行车时请勿开启后备舱</string>
    <string name="driving_no_open_sky">行车时请勿开启后穹顶</string>
    <string name="driving_sound">低速提示音</string>
    <string name="economy">经济</string>
    <string name="edit">编辑</string>
    <string name="eight_second_countdown">15S未操作将自动退出</string>
    <string name="eight_second_countdown2">S未操作将自动退出</string>
    <string name="empty_shade_text">No notifications</string>
    <string name="enable_adb">USB debugging</string>
    <string name="enable_adb_summary">Debug mode when USB is connected</string>
    <string name="enable_adb_wireless">Wireless debugging</string>
    <string name="enable_adb_wireless_summary">Debug mode when Wi‑Fi is connected</string>
    <string name="enable_bluetooth_confirmation_ok">Turn on</string>
    <string name="enable_bluetooth_message">To connect your keyboard with your tablet, you first have to turn on Bluetooth.</string>
    <string name="enable_bluetooth_title">Turn on Bluetooth?</string>
    <string name="enable_demo_mode">Enable demo mode</string>
    <string name="enable_freeform_support">Enable freeform windows</string>
    <string name="enable_freeform_support_summary">Enable support for experimental freeform windows.</string>
    <string name="enable_gpu_debug_layers">Enable GPU debug layers</string>
    <string name="enable_gpu_debug_layers_summary">Allow loading GPU debug layers for debug apps</string>
    <string name="enable_opengl_traces_title">Enable OpenGL traces</string>
    <string name="enable_terminal_summary">Enable terminal app that offers local shell access</string>
    <string name="enable_terminal_title">Local terminal</string>
    <string name="enable_verbose_vendor_logging">Enable verbose vendor logging</string>
    <string name="enable_verbose_vendor_logging_summary">Include additional device-specific vendor logs in bug reports, which may contain private information, use more battery, and/or use more storage.</string>
    <string name="enabled_by_admin">Enabled by admin</string>
    <string name="energy_select_cltc">CLTC</string>
    <string name="energy_select_wltc">WLTC</string>
    <string name="enhanced_connectivity_summary">Enables the Enhanced Connectivity feature.</string>
    <string name="enter_washing">进入洗车模式</string>
    <string name="error_disable_esim_msg">The eSIM can’t be disabled due to an error.</string>
    <string name="error_disable_esim_title">Can’t disable eSIM</string>
    <string name="error_icon_content_description">Error</string>
    <string name="error_msg_power_off_gear">下电失败，请挂入P/N挡后重试</string>
    <string name="error_msg_power_off_gear_skylight">行驶过程中请勿开启后备舱</string>
    <string name="error_msg_power_off_speedValue">下电失败，请降低车速后重试</string>
    <string name="ethernet_label">Ethernet</string>
    <string name="exit">退出</string>
    <string name="exit_washing">确定退出洗车模式吗？</string>
    <string name="exit_washing_mode_success">已成功退出洗车模式</string>
    <string name="expand_button_title">Advanced</string>
    <string name="expanded_header_battery_charged">Charged</string>
    <string name="expanded_header_battery_charging">Charging</string>
    <string name="expanded_header_battery_charging_with_time">%s until full</string>
    <string name="expanded_header_battery_not_charging">Not charging</string>
    <string name="experimental">Experimental</string>
    <string name="exposed_dropdown_menu_content_description">Show dropdown menu</string>
    <string name="external_source_trusted">Allowed</string>
    <string name="external_source_untrusted">Not allowed</string>
    <string name="eye_protection_mode">色温调节</string>
    <string name="eye_protection_mode_cold">偏冷</string>
    <string name="eye_protection_mode_warm">偏暖</string>
    <string name="fab_transformation_scrim_behavior">com.google.android.material.transformation.FabTransformationScrimBehavior</string>
    <string name="fab_transformation_sheet_behavior">com.google.android.material.transformation.FabTransformationSheetBehavior</string>
    <string name="face_dialog_looking_for_face">Looking for you…</string>
    <string name="failed_to_open_app_settings_toast">Failed to open settings for %1$s</string>
    <string name="fingerprint_dialog_touch_sensor">Touch the fingerprint sensor</string>
    <string name="finish">完成</string>
    <string name="fog_lights">后雾灯</string>
    <string name="force_allow_on_external">Force allow apps on external</string>
    <string name="force_allow_on_external_summary">Makes any app eligible to be written to external storage, regardless of manifest values</string>
    <string name="force_msaa">Force 4x MSAA</string>
    <string name="force_msaa_summary">Enable 4x MSAA in OpenGL ES 2.0 apps</string>
    <string name="force_resizable_activities">Force activities to be resizable</string>
    <string name="force_resizable_activities_summary">Make all activities resizable for multi-window, regardless of manifest values.</string>
    <string name="force_rtl_layout_all_locales">Force RTL layout direction</string>
    <string name="force_rtl_layout_all_locales_summary">Force screen layout direction to RTL for all locales</string>
    <string name="forced_resizable_secondary_display">App may not work on a secondary display.</string>
    <string name="front_windshield_wiper">雨刮</string>
    <string name="front_windshield_wiper_mode">模式</string>
    <string name="fuel">油量</string>
    <string name="full_lock">全锁</string>
    <string name="game_console">游戏主机</string>
    <string name="garbage_cleanup">内存清理</string>
    <string name="gearControlTip_1">自动驻车以防止溜坡，系安全带并关闭车门，以便保持挡位</string>
    <string name="gearControlTip_2">踩下制动踏板以切换挡位</string>
    <string name="gearControlTip_3">车速太高，请减速后再切换挡位</string>
    <string name="gearControlTip_4">驻车中，请勿松开制动踏板</string>
    <string name="gearControlTip_5">释放中，请勿松开制动踏板条件</string>
    <string name="gearControlTip_6">请踩住刹车操作换挡</string>
    <string name="gear_dissatisfaction_requirement">挡位不满足要求</string>
    <string name="global_action_lock_message">Unlock your phone for more options</string>
    <string name="global_action_screenshot">Screenshot</string>
    <string name="go_recorder">前往行车记录仪</string>
    <string name="go_to_web">Go to browser</string>
    <string name="got_it">Got it</string>
    <string name="gps_notification_found_text">Location set by GPS</string>
    <string name="gps_notification_searching_text">Searching for GPS</string>
    <string name="guest_exit_guest">Remove guest</string>
    <string name="guest_exit_guest_dialog_message">All apps and data in this session will be deleted.</string>
    <string name="guest_exit_guest_dialog_remove">Remove</string>
    <string name="guest_exit_guest_dialog_title">Remove guest?</string>
    <string name="guest_new_guest">Add guest</string>
    <string name="guest_nickname">Guest</string>
    <string name="guest_notification_remove_action">REMOVE GUEST</string>
    <string name="guest_notification_text">To delete apps and data, remove guest user</string>
    <string name="guest_notification_title">Guest user</string>
    <string name="guest_wipe_session_dontwipe">Yes, continue</string>
    <string name="guest_wipe_session_message">Do you want to continue your session?</string>
    <string name="guest_wipe_session_title">Welcome back, guest!</string>
    <string name="guest_wipe_session_wipe">Start over</string>
    <string name="hdcp_checking_dialog_title">Set HDCP checking behavior</string>
    <string name="hdcp_checking_title">HDCP checking</string>
    <string name="headlights">大灯</string>
    <string name="headset">Headset</string>
    <string name="heap_dump_tile_name">Dump SysUI Heap</string>
    <string name="heavy">重</string>
    <string name="help_feedback_label">Help &amp; feedback</string>
    <string name="help_label">Help &amp; feedback</string>
    <string name="help_uri_battery_saver_learn_more_link_target" />
    <string name="help_url_imsi_protection" />
    <string name="hidden_notifications_cancel">No thanks</string>
    <string name="hidden_notifications_setup">Set up</string>
    <string name="hidden_notifications_text">See them before you unlock</string>
    <string name="hidden_notifications_title">Get notifications faster</string>
    <string name="hide_bottom_view_on_scroll_behavior">com.google.android.material.behavior.HideBottomViewOnScrollBehavior</string>
    <string name="high">高</string>
    <string name="high_speed_no_rear">为保障安全,高速行驶时无法折叠</string>
    <string name="high_temp_alarm_help_care_steps">See care steps</string>
    <string name="high_temp_alarm_help_url">help_uri_usb_warm</string>
    <string name="high_temp_alarm_notify_message">There’s an issue charging this device. Unplug the power adapter, and take care as the cable may be warm.</string>
    <string name="high_temp_alarm_title">Unplug charger</string>
    <string name="high_temp_dialog_help_text">See care steps</string>
    <string name="high_temp_dialog_help_url" />
    <string name="high_temp_dialog_message">Your phone will automatically try to cool down. You can still use your phone, but it may run slower.\n\nOnce your phone has cooled down, it will run normally.</string>
    <string name="high_temp_notif_message">Some features limited while phone cools down.\nTap for more info</string>
    <string name="high_temp_title">Phone is getting warm</string>
    <string name="home">Settings Home</string>
    <string name="hud">HUD</string>
    <string name="icon_content_description">Dialog Icon</string>
    <string name="ime_security_warning">This input method may be able to collect all the text you type, including personal data like passwords and credit card numbers. It comes from the app %1$s. Use this input method?</string>
    <string name="immediately_destroy_activities">Don’t keep activities</string>
    <string name="immediately_destroy_activities_summary">Destroy every activity as soon as the user leaves it</string>
    <string name="ims_reg_status_not_registered">Not registered</string>
    <string name="ims_reg_status_registered">Registered</string>
    <string name="ims_reg_title">IMS registration state</string>
    <string name="imsi_protection_warning">This network receives a SIM ID that can be used to track device location. Learn more</string>
    <string name="inactive_app_active_summary">Active. Tap to toggle.</string>
    <string name="inactive_app_inactive_summary">Inactive. Tap to toggle.</string>
    <string name="inactive_apps_title">Standby apps</string>
    <string name="inattentive_sleep_warning_message">The device will soon turn off; press to keep it on.</string>
    <string name="inattentive_sleep_warning_title">Standby</string>
    <string name="inline_block_button">Block</string>
    <string name="inline_blocking_helper">You usually dismiss these notifications. \nKeep showing them?</string>
    <string name="inline_deliver_silently_button">Deliver Silently</string>
    <string name="inline_done_button">Done</string>
    <string name="inline_keep_button">Keep showing</string>
    <string name="inline_keep_showing">Keep showing these notifications?</string>
    <string name="inline_keep_showing_app">Keep showing notifications from this app?</string>
    <string name="inline_minimize_button">Minimize</string>
    <string name="inline_ok_button">Apply</string>
    <string name="inline_silent_button_alert">Alerting</string>
    <string name="inline_silent_button_keep_alerting">Keep alerting</string>
    <string name="inline_silent_button_silent">Silent</string>
    <string name="inline_silent_button_stay_silent">Stay silent</string>
    <string name="inline_stop_button">Stop notifications</string>
    <string name="inline_turn_off_notifications">Turn off notifications</string>
    <string name="inline_undo">Undo</string>
    <string name="install_other_apps">Install unknown apps</string>
    <string name="installer_cd_button_title">Install Android File Transfer app for Mac</string>
    <string name="instant_apps">Instant Apps</string>
    <string name="instant_apps_help_url" />
    <string name="instant_apps_message">App opened without being installed.</string>
    <string name="instant_apps_message_with_help">App opened without being installed. Tap to learn more.</string>
    <string name="instant_apps_title">%1$s running</string>
    <string name="interruption_level_alarms">Alarms only</string>
    <string name="interruption_level_alarms_twoline">Alarms\nonly</string>
    <string name="interruption_level_none">Total silence</string>
    <string name="interruption_level_none_twoline">Total\nsilence</string>
    <string name="interruption_level_none_with_warning">Total silence. This will also silence screen readers.</string>
    <string name="interruption_level_priority">Priority only</string>
    <string name="interruption_level_priority_twoline">Priority\nonly</string>
    <string name="invalid_charger">Can\'t charge via USB. Use the charger that came with your device.</string>
    <string name="invalid_charger_text">Use the charger that came with your device</string>
    <string name="invalid_charger_title">Can\'t charge via USB</string>
    <string name="is_synchronizing">正在同步通讯录</string>
    <string name="item_view_role_description">Tab</string>
    <string name="keep_screen_on">Stay awake</string>
    <string name="keep_screen_on_summary">Screen will never sleep while charging</string>
    <string name="keyboard_key_back">Back</string>
    <string name="keyboard_key_backspace">Backspace</string>
    <string name="keyboard_key_button_template">Button %1$s</string>
    <string name="keyboard_key_dpad_center">Center</string>
    <string name="keyboard_key_dpad_down">Down</string>
    <string name="keyboard_key_dpad_left">Left</string>
    <string name="keyboard_key_dpad_right">Right</string>
    <string name="keyboard_key_dpad_up">Up</string>
    <string name="keyboard_key_enter">Enter</string>
    <string name="keyboard_key_forward_del">Delete</string>
    <string name="keyboard_key_home">Home</string>
    <string name="keyboard_key_insert">Insert</string>
    <string name="keyboard_key_media_fast_forward">Fast Forward</string>
    <string name="keyboard_key_media_next">Next</string>
    <string name="keyboard_key_media_play_pause">Play/Pause</string>
    <string name="keyboard_key_media_previous">Previous</string>
    <string name="keyboard_key_media_rewind">Rewind</string>
    <string name="keyboard_key_media_stop">Stop</string>
    <string name="keyboard_key_move_end">End</string>
    <string name="keyboard_key_move_home">Home</string>
    <string name="keyboard_key_num_lock">Num Lock</string>
    <string name="keyboard_key_numpad_template">Numpad %1$s</string>
    <string name="keyboard_key_page_down">Page Down</string>
    <string name="keyboard_key_page_up">Page Up</string>
    <string name="keyboard_key_space">Space</string>
    <string name="keyboard_key_tab">Tab</string>
    <string name="keyboard_shortcut_group_applications">Applications</string>
    <string name="keyboard_shortcut_group_applications_assist">Assist</string>
    <string name="keyboard_shortcut_group_applications_browser">Browser</string>
    <string name="keyboard_shortcut_group_applications_calendar">Calendar</string>
    <string name="keyboard_shortcut_group_applications_contacts">Contacts</string>
    <string name="keyboard_shortcut_group_applications_email">Email</string>
    <string name="keyboard_shortcut_group_applications_music">Music</string>
    <string name="keyboard_shortcut_group_applications_sms">SMS</string>
    <string name="keyboard_shortcut_group_applications_youtube">YouTube</string>
    <string name="keyboard_shortcut_group_system">System</string>
    <string name="keyboard_shortcut_group_system_back">Back</string>
    <string name="keyboard_shortcut_group_system_home">Home</string>
    <string name="keyboard_shortcut_group_system_notifications">Notifications</string>
    <string name="keyboard_shortcut_group_system_recents">Recents</string>
    <string name="keyboard_shortcut_group_system_shortcuts_helper">Keyboard Shortcuts</string>
    <string name="keyboard_shortcut_group_system_switch_input">Switch keyboard layout</string>
    <string name="keyboardview_keycode_delete">Delete</string>
    <string name="keyboardview_keycode_enter">Enter</string>
    <string name="keyguard_accessibility_next_alarm">Next alarm set for %1$s</string>
    <string name="keyguard_accessibility_password">Device password</string>
    <string name="keyguard_accessibility_pin_area">PIN area</string>
    <string name="keyguard_accessibility_sim_pin_area">SIM PIN area</string>
    <string name="keyguard_accessibility_sim_puk_area">SIM PUK area</string>
    <string name="keyguard_carrier_default">No service.</string>
    <string name="keyguard_carrier_name_with_sim_locked_template" formatted="false">%s (%s)</string>
    <string name="keyguard_charged">Charged</string>
    <string name="keyguard_enter_your_password">Enter your password</string>
    <string name="keyguard_enter_your_pattern">Enter your pattern</string>
    <string name="keyguard_enter_your_pin">Enter your PIN</string>
    <string name="keyguard_fancy_colon" />
    <string name="keyguard_indication_charging_time">%2$s • Charging (%1$s until full)</string>
    <string name="keyguard_indication_charging_time_fast">%2$s • Charging rapidly (%1$s until full)</string>
    <string name="keyguard_indication_charging_time_slowly">%2$s • Charging slowly (%1$s until full)</string>
    <string name="keyguard_indication_charging_time_wireless">%2$s • Charging wirelessly (%1$s until full)</string>
    <string name="keyguard_indication_trust_disabled">Device will stay locked until you manually unlock</string>
    <string name="keyguard_indication_trust_unlocked">Kept unlocked by TrustAgent</string>
    <string name="keyguard_indication_trust_unlocked_plugged_in">%1$s\n%2$s</string>
    <string name="keyguard_instructions_when_pattern_disabled">Press Menu to unlock.</string>
    <string name="keyguard_low_battery">Connect your charger.</string>
    <string name="keyguard_missing_sim_instructions">Insert a SIM card.</string>
    <string name="keyguard_missing_sim_instructions_long">The SIM card is missing or not readable. Insert a SIM card.</string>
    <string name="keyguard_missing_sim_message">No SIM card in phone.</string>
    <string name="keyguard_missing_sim_message_short">No SIM card</string>
    <string name="keyguard_more_overflow_text">+%d</string>
    <string name="keyguard_network_locked_message">Network locked</string>
    <string name="keyguard_password_enter_password_code">Type password to unlock</string>
    <string name="keyguard_password_enter_pin_code">Type PIN code</string>
    <string name="keyguard_password_enter_pin_password_code">Type PIN to unlock</string>
    <string name="keyguard_password_enter_pin_prompt">New SIM PIN code</string>
    <string name="keyguard_password_enter_puk_code">Type SIM PUK and new PIN code</string>
    <string name="keyguard_password_enter_puk_prompt">SIM PUK code</string>
    <string name="keyguard_password_entry_touch_hint">Touch to type password</string>
    <string name="keyguard_password_wrong_pin_code">Incorrect PIN code.</string>
    <string name="keyguard_permanent_disabled_sim_instructions">Your SIM card has been permanently disabled.\n Contact your wireless service provider for another SIM card.</string>
    <string name="keyguard_permanent_disabled_sim_message_short">Unusable SIM card.</string>
    <string name="keyguard_plugged_in">%s • Charging</string>
    <string name="keyguard_plugged_in_charging_fast">%s • Charging rapidly</string>
    <string name="keyguard_plugged_in_charging_limited">%s • Optimizing for battery health</string>
    <string name="keyguard_plugged_in_charging_slowly">%s • Charging slowly</string>
    <string name="keyguard_plugged_in_wireless">%s • Charging wirelessly</string>
    <string name="keyguard_retry">Swipe up to try again</string>
    <string name="keyguard_sim_error_message_short">Invalid Card.</string>
    <string name="keyguard_sim_locked_message">SIM card is locked.</string>
    <string name="keyguard_sim_puk_locked_message">SIM card is PUK-locked.</string>
    <string name="keyguard_sim_unlock_progress_dialog_message">Unlocking SIM card…</string>
    <string name="keyguard_unlock">Swipe up to open</string>
    <string name="keyguard_widget_12_hours_format">h:mm</string>
    <string name="keyguard_widget_24_hours_format">kk:mm</string>
    <string name="keyless_auth_content">请在2分钟内踩踏板挂挡，获得一次性驾驶权限</string>
    <string name="keyless_auth_title">无钥匙启动授权</string>
    <string name="keywords_adb_wireless">adb, debug, dev</string>
    <string name="kg_enter_confirm_pin_hint">Confirm desired PIN code</string>
    <string name="kg_face_not_recognized">Not recognized</string>
    <string name="kg_failed_attempts_almost_at_erase_profile">You have incorrectly attempted to unlock the phone %1$d times. After %2$d more unsuccessful attempts, the work profile will be removed, which will delete all profile data.</string>
    <string name="kg_failed_attempts_almost_at_erase_user">You have incorrectly attempted to unlock the phone %1$d times. After %2$d more unsuccessful attempts, this user will be removed, which will delete all user data.</string>
    <string name="kg_failed_attempts_almost_at_login">You have incorrectly drawn your unlock pattern %1$d times. After %2$d more unsuccessful attempts, you will be asked to unlock your phone using an email account.\n\n Try again in %3$d seconds.</string>
    <string name="kg_failed_attempts_almost_at_wipe">You have incorrectly attempted to unlock the phone %1$d times. After %2$d more unsuccessful attempts, this phone will be reset, which will delete all its data.</string>
    <string name="kg_failed_attempts_now_erasing_profile">You have incorrectly attempted to unlock the phone %d times. The work profile will be removed, which will delete all profile data.</string>
    <string name="kg_failed_attempts_now_erasing_user">You have incorrectly attempted to unlock the phone %d times. This user will be removed, which will delete all user data.</string>
    <string name="kg_failed_attempts_now_wiping">You have incorrectly attempted to unlock the phone %d times. This phone will be reset, which will delete all its data.</string>
    <string name="kg_fingerprint_not_recognized">Not recognized</string>
    <string name="kg_forgot_pattern_button_text">Forgot Pattern</string>
    <string name="kg_invalid_confirm_pin_hint">PIN codes does not match</string>
    <string name="kg_invalid_puk">Re-enter the correct PUK code. Repeated attempts will permanently disable the SIM.</string>
    <string name="kg_invalid_sim_pin_hint">Type a PIN that is 4 to 8 numbers.</string>
    <string name="kg_invalid_sim_puk_hint">PUK code should be 8 numbers or more.</string>
    <string name="kg_login_too_many_attempts">Too many pattern attempts</string>
    <string name="kg_password_instructions">Enter Password</string>
    <string name="kg_password_pin_failed">SIM PIN operation failed!</string>
    <string name="kg_password_puk_failed">SIM PUK operation failed!</string>
    <string name="kg_password_wrong_pin_code_pukked">Incorrect SIM PIN code you must now contact your carrier to unlock your device.</string>
    <string name="kg_password_wrong_puk_code_dead">SIM is unusable. Contact your carrier.</string>
    <string name="kg_pattern_instructions">Draw your pattern</string>
    <string name="kg_pin_accepted">Code Accepted!</string>
    <string name="kg_pin_instructions">Enter PIN</string>
    <string name="kg_prompt_reason_device_admin">Device locked by admin</string>
    <string name="kg_prompt_reason_restart_password">Password required after device restarts</string>
    <string name="kg_prompt_reason_restart_pattern">Pattern required after device restarts</string>
    <string name="kg_prompt_reason_restart_pin">PIN required after device restarts</string>
    <string name="kg_prompt_reason_switch_profiles_password">Password required when you switch profiles</string>
    <string name="kg_prompt_reason_switch_profiles_pattern">Pattern required when you switch profiles</string>
    <string name="kg_prompt_reason_switch_profiles_pin">PIN required when you switch profiles</string>
    <string name="kg_prompt_reason_timeout_password">Password required for additional security</string>
    <string name="kg_prompt_reason_timeout_pattern">Pattern required for additional security</string>
    <string name="kg_prompt_reason_timeout_pin">PIN required for additional security</string>
    <string name="kg_prompt_reason_user_request">Device was locked manually</string>
    <string name="kg_puk_enter_pin_hint">Enter desired PIN code</string>
    <string name="kg_puk_enter_puk_hint">SIM is now disabled. Enter PUK code to continue. Contact carrier for details.</string>
    <string name="kg_puk_enter_puk_hint_multi">SIM \"%1$s\" is now disabled. Enter PUK code to continue. Contact carrier for details.</string>
    <string name="kg_sim_lock_esim_instructions">%1$s Disable eSIM to use device without mobile service.</string>
    <string name="kg_sim_pin_instructions">Enter SIM PIN.</string>
    <string name="kg_sim_pin_instructions_multi">Enter SIM PIN for \"%1$s\".</string>
    <string name="kg_sim_unlock_progress_dialog_message">Unlocking SIM card…</string>
    <string name="kg_too_many_failed_password_attempts_dialog_message">You have incorrectly typed your password %1$d times. \n\nTry again in %2$d seconds.</string>
    <string name="kg_too_many_failed_pattern_attempts_dialog_message">You have incorrectly drawn your unlock pattern %1$d times. \n\nTry again in %2$d seconds.</string>
    <string name="kg_too_many_failed_pin_attempts_dialog_message">You have incorrectly typed your PIN %1$d times. \n\nTry again in %2$d seconds.</string>
    <string name="kg_wrong_password">Wrong password</string>
    <string name="kg_wrong_pattern">Wrong pattern</string>
    <string name="kg_wrong_pin">Wrong PIN</string>
    <string name="konwn">知道了(%ds)</string>
    <string name="label_view">View</string>
    <string name="launch_defaults_none">No defaults set</string>
    <string name="launch_defaults_some">Some defaults set</string>
    <string name="launcher_overlayable_package">com.android.launcher3</string>
    <string name="lb_control_display_fast_forward_multiplier">%1$dX</string>
    <string name="lb_control_display_rewind_multiplier">%1$dX</string>
    <string name="lb_guidedaction_continue_title">Continue</string>
    <string name="lb_guidedaction_finish_title">Finish</string>
    <string name="lb_media_player_error">MediaPlayer error code %1$d extra %2$d</string>
    <string name="lb_navigation_menu_contentDescription">Navigation menu</string>
    <string name="lb_onboarding_accessibility_next">Next</string>
    <string name="lb_onboarding_get_started">GET STARTED</string>
    <string name="lb_playback_controls_closed_captioning_disable">Disable Closed Captioning</string>
    <string name="lb_playback_controls_closed_captioning_enable">Enable Closed Captioning</string>
    <string name="lb_playback_controls_fast_forward">Fast Forward</string>
    <string name="lb_playback_controls_fast_forward_multiplier">Fast Forward %1$dX</string>
    <string name="lb_playback_controls_hidden">Media controls hidden, press d-pad to show</string>
    <string name="lb_playback_controls_high_quality_disable">Disable High Quality</string>
    <string name="lb_playback_controls_high_quality_enable">Enable High Quality</string>
    <string name="lb_playback_controls_more_actions">More Actions</string>
    <string name="lb_playback_controls_pause">Pause</string>
    <string name="lb_playback_controls_picture_in_picture">Enter Picture In Picture Mode</string>
    <string name="lb_playback_controls_play">Play</string>
    <string name="lb_playback_controls_repeat_all">Repeat All</string>
    <string name="lb_playback_controls_repeat_none">Repeat None</string>
    <string name="lb_playback_controls_repeat_one">Repeat One</string>
    <string name="lb_playback_controls_rewind">Rewind</string>
    <string name="lb_playback_controls_rewind_multiplier">Rewind %1$dX</string>
    <string name="lb_playback_controls_shown">Media controls shown</string>
    <string name="lb_playback_controls_shuffle_disable">Disable Shuffle</string>
    <string name="lb_playback_controls_shuffle_enable">Enable Shuffle</string>
    <string name="lb_playback_controls_skip_next">Skip Next</string>
    <string name="lb_playback_controls_skip_previous">Skip Previous</string>
    <string name="lb_playback_controls_thumb_down">Deselect Thumb Down</string>
    <string name="lb_playback_controls_thumb_down_outline">Select Thumb Down</string>
    <string name="lb_playback_controls_thumb_up">Deselect Thumb Up</string>
    <string name="lb_playback_controls_thumb_up_outline">Select Thumb Up</string>
    <string name="lb_playback_time_separator">/</string>
    <string name="lb_search_bar_hint">Search</string>
    <string name="lb_search_bar_hint_speech">Speak to search</string>
    <string name="lb_search_bar_hint_with_title">Search %1$s</string>
    <string name="lb_search_bar_hint_with_title_speech">Speak to search %1$s</string>
    <string name="learn_more">Learn more</string>
    <string name="left">左</string>
    <string name="left_back">左后</string>
    <string name="left_icon">Left icon</string>
    <string name="left_keycode">Left keycode</string>
    <string name="left_nav_bar_button_type">Extra left button type</string>
    <string name="left_rearview_mirror">左后视镜</string>
    <string name="legacy_vpn_name">VPN</string>
    <string name="light">轻</string>
    <string name="light_color">浅色</string>
    <string name="loading_injected_setting_summary">Loading…</string>
    <string name="local_backup_password_summary_change">Tap to change or remove the password for desktop full backups</string>
    <string name="local_backup_password_summary_none">Desktop full backups aren’t currently protected</string>
    <string name="local_backup_password_title">Desktop backup password</string>
    <string name="local_backup_password_toast_confirmation_mismatch">New password and confirmation don’t match</string>
    <string name="local_backup_password_toast_success">New backup password set</string>
    <string name="local_backup_password_toast_validation_failure">Failure setting backup password</string>
    <string name="lockscreen_none">None</string>
    <string name="lockscreen_shortcut_left">Left shortcut</string>
    <string name="lockscreen_shortcut_right">Right shortcut</string>
    <string name="lockscreen_unlock_left">Left shortcut also unlocks</string>
    <string name="lockscreen_unlock_right">Right shortcut also unlocks</string>
    <string name="long_click_to_close">长按关闭</string>
    <string name="long_click_to_open">长按开启</string>
    <string name="low">低</string>
    <string name="magnification_controls_title">Magnification Window Controls</string>
    <string name="magnification_overlay_title">Magnification Overlay Window</string>
    <string name="magnification_window_title">Magnification Window</string>
    <string name="manage_bubbles_text">Manage</string>
    <string name="manage_notifications_history_text">History</string>
    <string name="manage_notifications_text">Manage</string>
    <string name="managed_user_title">All work apps</string>
    <string name="manual">手动</string>
    <string name="match_bluetooth">配对蓝牙设备</string>
    <string name="match_content">当前有其他设备正在配对中</string>
    <string name="material_slider_range_end">Range end,</string>
    <string name="material_slider_range_start">Range start,</string>
    <string name="media_category">Media</string>
    <string name="media_output_dialog_add_output">Add outputs</string>
    <string name="media_output_dialog_connect_failed">Couldn\'t connect. Try again.</string>
    <string name="media_output_dialog_disconnected">%1$s (disconnected)</string>
    <string name="media_output_dialog_group">Group</string>
    <string name="media_output_dialog_multiple_devices">%1$d devices selected</string>
    <string name="media_output_dialog_pairing_new">Pair new device</string>
    <string name="media_output_dialog_single_device">1 device selected</string>
    <string name="media_projection_action_text">Start now</string>
    <string name="media_projection_dialog_service_text">The service providing this function will have access to all of the information that is visible on your screen or played from your device while recording or casting. This includes information such as passwords, payment details, photos, messages, and audio that you play.</string>
    <string name="media_projection_dialog_service_title">Start recording or casting?</string>
    <string name="media_projection_dialog_text">%s will have access to all of the information that is visible on your screen or played from your device while recording or casting. This includes information such as passwords, payment details, photos, messages, and audio that you play.</string>
    <string name="media_projection_dialog_title">Start recording or casting with %s?</string>
    <string name="media_projection_remember_text">Don\'t show again</string>
    <string name="media_seamless_remote_device">Device</string>
    <string name="media_transfer_this_device_name">Phone speaker</string>
    <string name="media_transfer_wired_device_name">Wired audio device</string>
    <string name="media_transfer_wired_usb_device_name">Wired headphone</string>
    <string name="memory_cleanup_completed">内存清理完成</string>
    <string name="memory_cleanup_failed">内存清理完成</string>
    <string name="menu_ime">Keyboard switcher</string>
    <string name="mic_active">Microphone Active</string>
    <string name="mic_permission">麦克风权限</string>
    <string name="middle">中</string>
    <string name="mirror_adjustment">后视镜调整</string>
    <string name="mirror_mirror">后视镜折叠</string>
    <string name="mland">Marshmallow Land</string>
    <string name="mobile_carrier_text_format">%1$s, %2$s</string>
    <string name="mobile_data">Mobile data</string>
    <string name="mobile_data_always_on">Mobile data always active</string>
    <string name="mobile_data_always_on_summary">Always keep mobile data active, even when Wi‑Fi is active (for fast network switching).</string>
    <string name="mobile_data_disable_message">You won’t have access to data or the internet through %s. Internet will only be available via Wi-Fi.</string>
    <string name="mobile_data_disable_message_default_carrier">your carrier</string>
    <string name="mobile_data_disable_title">Turn off mobile data?</string>
    <string name="mobile_data_text_format">%1$s — %2$s</string>
    <string name="mock_location_app">Select mock location app</string>
    <string name="mock_location_app_not_set">No mock location app set</string>
    <string name="mock_location_app_set">Mock location app: %1$s</string>
    <string name="mode_night_auto">自适应</string>
    <string name="monitoring_button_view_policies">View Policies</string>
    <string name="monitoring_description_app">You\'re connected to %1$s, which can monitor your network activity, including emails, apps, and websites.</string>
    <string name="monitoring_description_app_personal">You\'re connected to %1$s, which can monitor your personal network activity, including emails, apps, and websites.</string>
    <string name="monitoring_description_app_personal_work">Your work profile is managed by %1$s. The profile is connected to %2$s, which can monitor your work network activity, including emails, apps, and websites.\n\nYou\'re also connected to %3$s, which can monitor your personal network activity.</string>
    <string name="monitoring_description_app_work">Your work profile is managed by %1$s. The profile is connected to %2$s, which can monitor your work network activity, including emails, apps, and websites.\n\nFor more information, contact your admin.</string>
    <string name="monitoring_description_ca_cert_settings">Open trusted credentials</string>
    <string name="monitoring_description_ca_cert_settings_separator"> </string>
    <string name="monitoring_description_ca_certificate">A certificate authority is installed on this device. Your secure network traffic may be monitored or modified.</string>
    <string name="monitoring_description_do_body">Your admin can monitor and manage settings, corporate access, apps, data associated with your device, and your device\'s location information.</string>
    <string name="monitoring_description_do_body_vpn">You\'re connected to %1$s, which can monitor your network activity, including emails, apps, and websites.</string>
    <string name="monitoring_description_do_header_generic">Your device is managed by %1$s.</string>
    <string name="monitoring_description_do_header_with_name">%1$s uses %2$s to manage your device.</string>
    <string name="monitoring_description_do_learn_more">Learn more</string>
    <string name="monitoring_description_do_learn_more_separator"> </string>
    <string name="monitoring_description_managed_profile_ca_certificate">Your organization installed a certificate authority in your work profile. Your secure network traffic may be monitored or modified.</string>
    <string name="monitoring_description_managed_profile_named_vpn">Your work profile is connected to %1$s, which can monitor your network activity, including emails, apps, and websites.</string>
    <string name="monitoring_description_management">This device belongs to your organization.\n\nYour IT admin can monitor and manage settings, corporate access, apps, data associated with your device, and your device\'s location information.\n\nFor more information, contact your IT admin.</string>
    <string name="monitoring_description_management_ca_certificate">Your organization installed a certificate authority on this device. Your secure network traffic may be monitored or modified.</string>
    <string name="monitoring_description_management_network_logging">Your admin has turned on network logging, which monitors traffic on your device.</string>
    <string name="monitoring_description_named_management">This device belongs to %1$s.\n\nYour IT admin can monitor and manage settings, corporate access, apps, data associated with your device, and your device\'s location information.\n\nFor more information, contact your IT admin.</string>
    <string name="monitoring_description_named_vpn">You\'re connected to %1$s, which can monitor your network activity, including emails, apps, and websites.</string>
    <string name="monitoring_description_network_logging">Your admin has turned on network logging, which monitors traffic on your device.\n\nFor more information, contact your admin.</string>
    <string name="monitoring_description_personal_profile_named_vpn">Your personal profile is connected to %1$s, which can monitor your network activity, including emails, apps, and websites.</string>
    <string name="monitoring_description_two_named_vpns">You\'re connected to %1$s and %2$s, which can monitor your network activity, including emails, apps, and websites.</string>
    <string name="monitoring_description_vpn">You gave an app permission to set up a VPN connection.\n\nThis app can monitor your device and network activity, including emails, apps, and websites.</string>
    <string name="monitoring_description_vpn_profile_owned">Your work profile is managed by %1$s.\n\nYour admin is capable of monitoring your network activity including emails, apps, and websites.\n\nFor more information, contact your admin.\n\nYou\'re also connected to a VPN, which can monitor your network activity.</string>
    <string name="monitoring_description_vpn_settings">Open VPN settings</string>
    <string name="monitoring_description_vpn_settings_separator"> </string>
    <string name="monitoring_subtitle_ca_certificate">CA certificates</string>
    <string name="monitoring_subtitle_network_logging">Network logging</string>
    <string name="monitoring_subtitle_vpn">VPN</string>
    <string name="monitoring_title">Network monitoring</string>
    <string name="monitoring_title_device_owned">Device management</string>
    <string name="monitoring_title_profile_owned">Profile monitoring</string>
    <string name="mr_button_content_description">Cast button</string>
    <string name="mr_cast_button_connected">Cast button. Connected</string>
    <string name="mr_cast_button_connecting">Cast button. Connecting</string>
    <string name="mr_cast_button_disconnected">Cast button. Disconnected</string>
    <string name="mr_cast_dialog_title_view_placeholder">No info available</string>
    <string name="mr_chooser_searching">Finding devices</string>
    <string name="mr_chooser_title">Cast to</string>
    <string name="mr_controller_album_art">Album art</string>
    <string name="mr_controller_casting_screen">Casting screen</string>
    <string name="mr_controller_close_description">Close</string>
    <string name="mr_controller_collapse_group">Collapse</string>
    <string name="mr_controller_disconnect">Disconnect</string>
    <string name="mr_controller_expand_group">Expand</string>
    <string name="mr_controller_no_info_available">No info available</string>
    <string name="mr_controller_no_media_selected">No media selected</string>
    <string name="mr_controller_pause">Pause</string>
    <string name="mr_controller_play">Play</string>
    <string name="mr_controller_stop">Stop</string>
    <string name="mr_controller_stop_casting">Stop casting</string>
    <string name="mr_controller_volume_slider">Volume slider</string>
    <string name="mr_dialog_groupable_header">Add a device</string>
    <string name="mr_dialog_transferable_header">Play on a group</string>
    <string name="mr_system_route_name">System</string>
    <string name="mr_user_route_category_name">Devices</string>
    <string name="msg_bluetooth_connected">设备已连接</string>
    <string name="msg_connect_fail">连接设备失败，请确保设备未超出范围</string>
    <string name="msg_device_disconnected">蓝牙断开</string>
    <string name="msg_empty">内容不能为空</string>
    <string name="msg_no_devices">找不到可用的设备，请单击以刷新设备列表</string>
    <string name="msg_open_bluetooth">打开蓝牙以使用该功能</string>
    <string name="msg_pair_fail">无法配对设备，请重试</string>
    <string name="msg_pair_reach_limit">匹配列表已达到限制，请删除连接，然后重试</string>
    <string name="msg_remove_paired_device">请确保删除的配对信息\"%1$s\"?</string>
    <string name="msg_wait_for_connect">设备正在连接，请稍候</string>
    <string name="msg_wait_for_pairing">设备正在配对，请稍候</string>
    <string name="msg_warn_name_empty">输入名称不能为空，更改蓝牙名称失败</string>
    <string name="msg_warn_name_too_long">更改设备名称失败，最大长度为32</string>
    <string name="msg_wifiap_change_name">修改热点名称</string>
    <string name="msg_wifiap_change_password">修改连接密码</string>
    <string name="msg_wifiap_name">车机热点:</string>
    <string name="msg_wifiap_name_error">热点名称不符合设置规则，请重新设置</string>
    <string name="msg_wifiap_password">连接密码:</string>
    <string name="msg_wifiap_password_error">密码不符合设置规则，请重新设置</string>
    <string name="msg_wifiap_password_error2">热点密码为至少8位字母加数字的组合，请重新设置</string>
    <string name="mtrl_badge_numberless_content_description">New notification</string>
    <string name="mtrl_chip_close_icon_content_description">Remove %1$s</string>
    <string name="mtrl_exceed_max_badge_number_content_description">More than %1$d new notifications</string>
    <string name="mtrl_exceed_max_badge_number_suffix">%1$d%2$s</string>
    <string name="mtrl_picker_a11y_next_month">Change to next month</string>
    <string name="mtrl_picker_a11y_prev_month">Change to previous month</string>
    <string name="mtrl_picker_announce_current_selection">Current selection: %1$s</string>
    <string name="mtrl_picker_cancel">@android:string/cancel</string>
    <string name="mtrl_picker_confirm">@android:string/ok</string>
    <string name="mtrl_picker_date_header_selected">%1$s</string>
    <string name="mtrl_picker_date_header_title">Select a Date</string>
    <string name="mtrl_picker_date_header_unselected">Selected date</string>
    <string name="mtrl_picker_day_of_week_column_header">Column of days: %1$s</string>
    <string name="mtrl_picker_invalid_format">Invalid format.</string>
    <string name="mtrl_picker_invalid_format_example">Example: %1$s</string>
    <string name="mtrl_picker_invalid_format_use">Use: %1$s</string>
    <string name="mtrl_picker_invalid_range">Invalid range.</string>
    <string name="mtrl_picker_navigate_to_year_description">Navigate to year %1$s</string>
    <string name="mtrl_picker_out_of_range">Out of range: %1$s</string>
    <string name="mtrl_picker_range_header_only_end_selected">Start date – %1$s</string>
    <string name="mtrl_picker_range_header_only_start_selected">%1$s – End date</string>
    <string name="mtrl_picker_range_header_selected">%1$s – %2$s</string>
    <string name="mtrl_picker_range_header_title">Select a Date Range</string>
    <string name="mtrl_picker_range_header_unselected">Start date – End date</string>
    <string name="mtrl_picker_save">Save</string>
    <string name="mtrl_picker_text_input_date_hint">Date</string>
    <string name="mtrl_picker_text_input_date_range_end_hint">End date</string>
    <string name="mtrl_picker_text_input_date_range_start_hint">Start date</string>
    <string name="mtrl_picker_text_input_day_abbr">d</string>
    <string name="mtrl_picker_text_input_month_abbr">m</string>
    <string name="mtrl_picker_text_input_year_abbr">y</string>
    <string name="mtrl_picker_toggle_to_calendar_input_mode">Switch to calendar input mode</string>
    <string name="mtrl_picker_toggle_to_day_selection">Tap to switch to selecting a day</string>
    <string name="mtrl_picker_toggle_to_text_input_mode">Switch to text input mode</string>
    <string name="mtrl_picker_toggle_to_year_selection">Tap to switch to selecting a year</string>
    <string name="music_controls_no_title">No title</string>
    <string name="nav_bar">Navigation bar</string>
    <string name="nav_bar_default">(default)</string>
    <string name="nav_bar_edge_panel">Navigation bar Edge Panel</string>
    <string name="nav_bar_layout">Layout</string>
    <string name="net_error">网络异常</string>
    <string name="net_error_content">请检查网络，或点击重试</string>
    <string name="nextPage">指示灯翻页</string>
    <string name="no_application">Nothing</string>
    <string name="no_auto_saver_action">No thanks</string>
    <string name="no_satellite_signal">暂无卫星信号</string>
    <string name="no_shortcut">%1$s doesn’t support conversation features</string>
    <string name="no_tiles_add">No tiles to add</string>
    <string name="not_default_data_content_description">Not set to use data</string>
    <string name="not_set">Not set</string>
    <string name="notice">请打开大灯</string>
    <string name="notice_header" />
    <string name="notification_alert_title">Default</string>
    <string name="notification_app_settings">Customize</string>
    <string name="notification_appops_ok">OK</string>
    <string name="notification_appops_settings">Settings</string>
    <string name="notification_bubble_title">Bubble</string>
    <string name="notification_center">消息通知</string>
    <string name="notification_channel_alerts">Alerts</string>
    <string name="notification_channel_battery">Battery</string>
    <string name="notification_channel_controls_closed_accessibility">Notification controls for %1$s closed</string>
    <string name="notification_channel_controls_opened_accessibility">Notification controls for %1$s opened</string>
    <string name="notification_channel_dialog_title">All %1$s notifications</string>
    <string name="notification_channel_disabled">You won\'t see these notifications anymore</string>
    <string name="notification_channel_general">General Messages</string>
    <string name="notification_channel_hints">Hints</string>
    <string name="notification_channel_minimized">These notifications will be minimized</string>
    <string name="notification_channel_screenshot">Screenshots</string>
    <string name="notification_channel_silenced">These notifications will be shown silently</string>
    <string name="notification_channel_storage">Storage</string>
    <string name="notification_channel_summary_bubble">Keeps your attention with a floating shortcut to this content.</string>
    <string name="notification_channel_summary_default">May ring or vibrate based on phone settings</string>
    <string name="notification_channel_summary_default_with_bubbles">May ring or vibrate based on phone settings. Conversations from %1$s bubble by default.</string>
    <string name="notification_channel_summary_low">No sound or vibration</string>
    <string name="notification_channel_summary_priority">Shows at top of conversation section, appears as floating bubble, displays profile picture on lock screen</string>
    <string name="notification_channel_switch_accessibility">Allow notifications from this channel</string>
    <string name="notification_channel_tv_pip">Picture-in-Picture</string>
    <string name="notification_channel_unsilenced">These notifications will alert you</string>
    <string name="notification_content_gesture_nav_available">Go to Settings to update system navigation</string>
    <string name="notification_content_system_nav_changed">System navigation updated. To make changes, go to Settings.</string>
    <string name="notification_conversation_bubble">Show bubble</string>
    <string name="notification_conversation_channel_settings">Settings</string>
    <string name="notification_conversation_favorite">Important conversation</string>
    <string name="notification_conversation_home_screen">Add to home screen</string>
    <string name="notification_conversation_mute">Silenced</string>
    <string name="notification_conversation_summary_low">No sound or vibration and appears lower in conversation section</string>
    <string name="notification_conversation_unbubble">Remove bubbles</string>
    <string name="notification_conversation_unfavorite">Not an important conversation</string>
    <string name="notification_conversation_unmute">Alerting</string>
    <string name="notification_delegate_header">Proxied notification</string>
    <string name="notification_done">Done</string>
    <string name="notification_group_overflow_indicator">+ %s</string>
    <string name="notification_header_default_channel">Notifications</string>
    <string name="notification_menu_accessibility">%1$s %2$s</string>
    <string name="notification_menu_gear_description">notification controls</string>
    <string name="notification_menu_settings_action">Settings</string>
    <string name="notification_menu_snooze_action">Remind me</string>
    <string name="notification_menu_snooze_description">notification snooze options</string>
    <string name="notification_more_settings">More settings</string>
    <string name="notification_multichannel_desc">This group of notifications cannot be configured here</string>
    <string name="notification_priority_title">Priority</string>
    <string name="notification_section_header_alerting">Notifications</string>
    <string name="notification_section_header_conversations">Conversations</string>
    <string name="notification_section_header_gentle">Silent</string>
    <string name="notification_section_header_incoming">New</string>
    <string name="notification_silence_title">Silent</string>
    <string name="notification_summary_message_format">%1$s: %2$s</string>
    <string name="notification_tap_again">Tap again to open</string>
    <string name="notification_unblockable_desc">These notifications can\'t be modified.</string>
    <string name="num_2">2</string>
    <string name="oem_preferred_feedback_reporter" />
    <string name="oem_unlock_enable">OEM unlocking</string>
    <string name="oem_unlock_enable_summary">Allow the bootloader to be unlocked</string>
    <string name="ok">@android:string/ok</string>
    <string name="okay">OK</string>
    <string name="ongoing_privacy_chip_content_multiple_apps">Applications are using your %s.</string>
    <string name="ongoing_privacy_chip_content_single_app">%1$s is using your %2$s.</string>
    <string name="ongoing_privacy_dialog_last_separator"> and </string>
    <string name="ongoing_privacy_dialog_separator">, </string>
    <string name="open_on">开启</string>
    <string name="open_saver_setting_action">Settings</string>
    <string name="opening_please_not_click">重复请求</string>
    <string name="orb_search_action">Search Action</string>
    <string name="ordinary">标准</string>
    <string name="osu_completing_sign_up">Completing sign-up…</string>
    <string name="osu_connect_failed">Couldn’t connect</string>
    <string name="osu_opening_provider">Opening %1$s</string>
    <string name="osu_sign_up_complete">Sign-up complete. Connecting…</string>
    <string name="osu_sign_up_failed">Couldn’t complete sign-up. Tap to try again.</string>
    <string name="other">Other</string>
    <string name="other_equipment_content">其它设备</string>
    <string name="output_calls_title">Phone call output</string>
    <string name="output_none_found">No devices found</string>
    <string name="output_none_found_service_off">No devices found. Try turning on %1$s</string>
    <string name="output_service_bt">Bluetooth</string>
    <string name="output_service_bt_wifi">Bluetooth and Wi-Fi</string>
    <string name="output_service_wifi">Wi-Fi</string>
    <string name="output_title">Media output</string>
    <string name="overlay_display_devices_title">Simulate secondary displays</string>
    <string name="overview">Overview</string>
    <string name="p">P</string>
    <string name="pad">车载pad</string>
    <string name="password_toggle_content_description">Show password</string>
    <string name="path_password_eye">M12,4.5C7,4.5 2.73,7.61 1,12c1.73,4.39 6,7.5 11,7.5s9.27,-3.11 11,-7.5c-1.73,-4.39 -6,-7.5 -11,-7.5zM12,17c-2.76,0 -5,-2.24 -5,-5s2.24,-5 5,-5 5,2.24 5,5 -2.24,5 -5,5zM12,9c-1.66,0 -3,1.34 -3,3s1.34,3 3,3 3,-1.34 3,-3 -1.34,-3 -3,-3z</string>
    <string name="path_password_eye_mask_strike_through">M2,4.27 L19.73,22 L22.27,19.46 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_eye_mask_visible">M2,4.27 L2,4.27 L4.54,1.73 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_strike_through">M3.27,4.27 L19.74,20.74</string>
    <string name="permission_manager">应用权限管理</string>
    <string name="phone_hint">Swipe from icon for phone</string>
    <string name="phone_label">open phone</string>
    <string name="phone_name">本机名称</string>
    <string name="picking_mode_ing">装卸模式运动中，模式打开后重试</string>
    <string name="pickup">拓展模式</string>
    <string name="pickup_mode_ing">拓展模式运动中，模式打开后重试</string>
    <string name="picture_color_mode">Picture color mode</string>
    <string name="picture_color_mode_desc">Use sRGB</string>
    <string name="pip_close">Close PIP</string>
    <string name="pip_fullscreen">Full screen</string>
    <string name="pip_menu_bounds">************ 690</string>
    <string name="pip_menu_title">Menu</string>
    <string name="pip_notification_message">If you don\'t want %s to use this feature, tap to open settings and turn it off.</string>
    <string name="pip_notification_title">%s is in picture-in-picture</string>
    <string name="pip_notification_unknown_title">(No title program)</string>
    <string name="pip_pause">Pause</string>
    <string name="pip_phone_close">Close</string>
    <string name="pip_phone_dismiss_hint">Drag down to dismiss</string>
    <string name="pip_phone_expand">Expand</string>
    <string name="pip_phone_minimize">Minimize</string>
    <string name="pip_phone_settings">Settings</string>
    <string name="pip_play">Play</string>
    <string name="pip_settings_bounds">************ 1026</string>
    <string name="pip_skip_to_next">Skip to next</string>
    <string name="pip_skip_to_prev">Skip to previous</string>
    <string name="plearse_driver_moving">请在驻车情况下调节座椅位置</string>
    <string name="plugins">Plugins</string>
    <string name="pointer_location">Pointer location</string>
    <string name="pointer_location_summary">Screen overlay showing current touch data</string>
    <string name="position_light">示廓灯</string>
    <string name="power_charging">%1$s - %2$s</string>
    <string name="power_charging_duration">%1$s - %2$s until charged</string>
    <string name="power_charging_limited">%1$s - Optimizing for battery health</string>
    <string name="power_discharge_by">Should last until about %1$s (%2$s)</string>
    <string name="power_discharge_by_enhanced">Should last until about %1$s based on your usage (%2$s)</string>
    <string name="power_discharge_by_only">Should last until about %1$s</string>
    <string name="power_discharge_by_only_enhanced">Should last until about %1$s based on your usage</string>
    <string name="power_discharge_by_only_short">Until %1$s</string>
    <string name="power_discharging_duration">About %1$s left (%2$s)</string>
    <string name="power_discharging_duration_enhanced">About %1$s left based on your usage (%2$s)</string>
    <string name="power_notification_controls_description">With power notification controls, you can set an importance level from 0 to 5 for an app\'s notifications. \n\nLevel 5 \n- Show at the top of the notification list \n- Allow full screen interruption \n- Always peek \n\nLevel 4 \n- Prevent full screen interruption \n- Always peek \n\nLevel 3 \n- Prevent full screen interruption \n- Never peek \n\nLevel 2 \n- Prevent full screen interruption \n- Never peek \n- Never make sound and vibration \n\nLevel 1 \n- Prevent full screen interruption \n- Never peek \n- Never make sound or vibrate \n- Hide from lock screen and status bar \n- Show at the bottom of the notification list \n\nLevel 0 \n- Block all notifications from the app </string>
    <string name="power_off_info">下电后，需重新解锁车辆才能再次上电，确定下电吗？</string>
    <string name="power_off_the_vehicle">车辆下电</string>
    <string name="power_off_title">车辆下电</string>
    <string name="power_remaining_charging_duration_only">%1$s left until charged</string>
    <string name="power_remaining_duration_only">About %1$s left</string>
    <string name="power_remaining_duration_only_enhanced">About %1$s left based on your usage</string>
    <string name="power_remaining_duration_only_short">%1$s</string>
    <string name="power_remaining_duration_only_shutdown_imminent">Phone may shut down soon</string>
    <string name="power_remaining_duration_shutdown_imminent">Phone may shut down soon (%1$s)</string>
    <string name="power_remaining_less_than_duration">Less than %1$s left (%2$s)</string>
    <string name="power_remaining_less_than_duration_only">Less than %1$s left</string>
    <string name="power_remaining_more_than_subtext">More than %1$s left (%2$s)</string>
    <string name="power_remaining_only_more_than_subtext">More than %1$s left</string>
    <string name="power_remaining_settings_home_page">%1$s - %2$s</string>
    <string name="power_suggestion_battery_run_out">Battery may run out by %1$s</string>
    <string name="power_system_error_1">动力系统异常,请安全停车,联系客服</string>
    <string name="power_system_error_2">动力系统异常,请尽快检修</string>
    <string name="preference_copied">\"%1$s\" copied to clipboard.</string>
    <string name="preference_summary_default_combination">%1$s / %2$s</string>
    <string name="priority_onboarding_appear_as_bubble_text">Appear as a floating bubble on top of apps</string>
    <string name="priority_onboarding_behavior">Priority conversations will:</string>
    <string name="priority_onboarding_done_button_title">Got it</string>
    <string name="priority_onboarding_ignores_dnd_text">Interrupt Do Not Disturb</string>
    <string name="priority_onboarding_settings_button_title">Settings</string>
    <string name="priority_onboarding_show_at_top_text">Show at top of conversation section</string>
    <string name="priority_onboarding_show_avatar_text">Show profile picture on lock screen</string>
    <string name="priority_onboarding_title">Conversation set to priority</string>
    <string name="privacy_type_camera">camera</string>
    <string name="privacy_type_location">location</string>
    <string name="privacy_type_microphone">microphone</string>
    <string name="private_dns_broken">Private DNS server cannot be accessed</string>
    <string name="private_dns_mode_off">Off</string>
    <string name="private_dns_mode_opportunistic">Automatic</string>
    <string name="private_dns_mode_provider">Private DNS provider hostname</string>
    <string name="private_dns_mode_provider_failure">Couldn\'t connect</string>
    <string name="private_dns_mode_provider_hostname_hint">Enter hostname of DNS provider</string>
    <string name="process_kernel_label">Android OS</string>
    <string name="profile_connect_timeout_subtext">Problem connecting. Turn device off &amp; back on</string>
    <string name="profile_info_settings_title">Profile info</string>
    <string name="profile_owned_footer">Profile may be monitored</string>
    <string name="proximity_sensor_secondary_type" />
    <string name="proximity_sensor_type" />
    <string name="proxy_exclusionlist_hint">example.com,mycomp.test.com,localhost</string>
    <string name="proxy_hostname_hint">proxy.example.com</string>
    <string name="proxy_port_hint">8080</string>
    <string name="proxy_url_hint">https://www.example.com/proxy.pac</string>
    <string name="qs_customize">Allow long-press customize in Quick Settings</string>
    <string name="qs_customize_info">Info</string>
    <string name="qs_customize_remove">Remove</string>
    <string name="qs_dnd_keep">Keep</string>
    <string name="qs_dnd_prompt_app">Do Not Disturb was turned on by an app (%s).</string>
    <string name="qs_dnd_prompt_auto_rule">Do Not Disturb was turned on by an automatic rule (%s).</string>
    <string name="qs_dnd_prompt_auto_rule_app">Do Not Disturb was turned on by an automatic rule or app.</string>
    <string name="qs_dnd_replace">Replace</string>
    <string name="qs_dnd_until">Until %s</string>
    <string name="qs_edit">Edit</string>
    <string name="qs_paging">Use the new Quick Settings</string>
    <string name="qs_rearrange">Rearrange Quick Settings</string>
    <string name="qs_status_phone_muted">Phone muted</string>
    <string name="qs_status_phone_vibrate">Phone on vibrate</string>
    <string name="quick_control">快捷控制</string>
    <string name="quick_controls_setup_subtitle">Hold the Power button to access your controls</string>
    <string name="quick_controls_setup_title">Set up device controls</string>
    <string name="quick_controls_subtitle">Add controls for your connected devices</string>
    <string name="quick_controls_title">Device controls</string>
    <string name="quick_cooling">速冷</string>
    <string name="quick_settings">Quick Settings</string>
    <string name="quick_settings_bluetooth_detail_empty_text">No paired devices available</string>
    <string name="quick_settings_bluetooth_label">Bluetooth</string>
    <string name="quick_settings_bluetooth_multiple_devices_label">Bluetooth (%d Devices)</string>
    <string name="quick_settings_bluetooth_off_label">Bluetooth Off</string>
    <string name="quick_settings_bluetooth_secondary_label_audio">Audio</string>
    <string name="quick_settings_bluetooth_secondary_label_battery_level">%s battery</string>
    <string name="quick_settings_bluetooth_secondary_label_headset">Headset</string>
    <string name="quick_settings_bluetooth_secondary_label_hearing_aids">Hearing Aids</string>
    <string name="quick_settings_bluetooth_secondary_label_input">Input</string>
    <string name="quick_settings_bluetooth_secondary_label_transient">Turning on…</string>
    <string name="quick_settings_brightness_dialog_auto_brightness_label">AUTO</string>
    <string name="quick_settings_brightness_dialog_title">Brightness</string>
    <string name="quick_settings_brightness_label">Brightness</string>
    <string name="quick_settings_cast_detail_empty_text">No devices available</string>
    <string name="quick_settings_cast_device_default_description">Ready to cast</string>
    <string name="quick_settings_cast_device_default_name">Unnamed device</string>
    <string name="quick_settings_cast_no_wifi">Wi‑Fi not connected</string>
    <string name="quick_settings_cast_title">Screen Cast</string>
    <string name="quick_settings_casting">Casting</string>
    <string name="quick_settings_cellular_detail_data_limit">%s limit</string>
    <string name="quick_settings_cellular_detail_data_usage">Data usage</string>
    <string name="quick_settings_cellular_detail_data_used">%s used</string>
    <string name="quick_settings_cellular_detail_data_warning">%s warning</string>
    <string name="quick_settings_cellular_detail_over_limit">Over limit</string>
    <string name="quick_settings_cellular_detail_remaining_data">Remaining data</string>
    <string name="quick_settings_cellular_detail_title">Mobile data</string>
    <string name="quick_settings_color_space_label">Color correction mode</string>
    <string name="quick_settings_connected">Connected</string>
    <string name="quick_settings_connected_battery_level">Connected, battery %1$s</string>
    <string name="quick_settings_connecting">Connecting...</string>
    <string name="quick_settings_dark_mode_secondary_label_battery_saver">Battery Saver</string>
    <string name="quick_settings_dark_mode_secondary_label_on_at">On at %s</string>
    <string name="quick_settings_dark_mode_secondary_label_on_at_sunset">On at sunset</string>
    <string name="quick_settings_dark_mode_secondary_label_until">Until %s</string>
    <string name="quick_settings_dark_mode_secondary_label_until_sunrise">Until sunrise</string>
    <string name="quick_settings_disclosure_managed_profile_monitoring">Your organization may monitor network traffic in your work profile</string>
    <string name="quick_settings_disclosure_managed_profile_named_vpn">Your work profile is connected to %1$s</string>
    <string name="quick_settings_disclosure_management">This device belongs to your organization</string>
    <string name="quick_settings_disclosure_management_monitoring">Your organization owns this device and may monitor network traffic</string>
    <string name="quick_settings_disclosure_management_named_vpn">This device belongs to your organization and is connected to %1$s</string>
    <string name="quick_settings_disclosure_management_vpns">This device belongs to your organization and is connected to VPNs</string>
    <string name="quick_settings_disclosure_monitoring">Network may be monitored</string>
    <string name="quick_settings_disclosure_named_managed_profile_monitoring">%1$s may monitor network traffic in your work profile</string>
    <string name="quick_settings_disclosure_named_management">This device belongs to %1$s</string>
    <string name="quick_settings_disclosure_named_management_monitoring">%1$s owns this device and may monitor network traffic</string>
    <string name="quick_settings_disclosure_named_management_named_vpn">This device belongs to %1$s and is connected to %2$s</string>
    <string name="quick_settings_disclosure_named_management_vpns">This device belongs to %1$s and is connected to VPNs</string>
    <string name="quick_settings_disclosure_named_vpn">This device is connected to %1$s</string>
    <string name="quick_settings_disclosure_personal_profile_named_vpn">Your personal profile is connected to %1$s</string>
    <string name="quick_settings_disclosure_vpns">This device is connected to VPNs</string>
    <string name="quick_settings_dnd_alarms_label">Alarms only</string>
    <string name="quick_settings_dnd_label">Do Not Disturb</string>
    <string name="quick_settings_dnd_none_label">Total silence</string>
    <string name="quick_settings_dnd_priority_label">Priority only</string>
    <string name="quick_settings_done">Done</string>
    <string name="quick_settings_flashlight_camera_in_use">Camera in use</string>
    <string name="quick_settings_flashlight_label">Flashlight</string>
    <string name="quick_settings_header_onboarding_text">Touch &amp; hold icons for more options</string>
    <string name="quick_settings_hotspot_label">Hotspot</string>
    <string name="quick_settings_hotspot_secondary_label_data_saver_enabled">Data Saver is on</string>
    <string name="quick_settings_hotspot_secondary_label_transient">Turning on…</string>
    <string name="quick_settings_ime_label">Input Method</string>
    <string name="quick_settings_inversion_label">Invert colors</string>
    <string name="quick_settings_location_label">Location</string>
    <string name="quick_settings_location_off_label">Location Off</string>
    <string name="quick_settings_media_device_label">Media device</string>
    <string name="quick_settings_more_settings">More settings</string>
    <string name="quick_settings_nfc_label">NFC</string>
    <string name="quick_settings_nfc_off">NFC is disabled</string>
    <string name="quick_settings_nfc_on">NFC is enabled</string>
    <string name="quick_settings_night_display_label">Night Light</string>
    <string name="quick_settings_night_secondary_label_on_at">On at %s</string>
    <string name="quick_settings_night_secondary_label_on_at_sunset">On at sunset</string>
    <string name="quick_settings_night_secondary_label_until_sunrise">Until sunrise</string>
    <string name="quick_settings_notifications_label">Notifications</string>
    <string name="quick_settings_reset_confirmation_button">Hide</string>
    <string name="quick_settings_reset_confirmation_message">It will reappear the next time you turn it on in settings.</string>
    <string name="quick_settings_reset_confirmation_title">Hide %1$s?</string>
    <string name="quick_settings_rotation_locked_label">Rotation locked</string>
    <string name="quick_settings_rotation_locked_landscape_label">Landscape</string>
    <string name="quick_settings_rotation_locked_portrait_label">Portrait</string>
    <string name="quick_settings_rotation_unlocked_label">Auto-rotate</string>
    <string name="quick_settings_rssi_emergency_only">Emergency Calls Only</string>
    <string name="quick_settings_rssi_label">RSSI</string>
    <string name="quick_settings_screen_record_label">Screen Record</string>
    <string name="quick_settings_screen_record_start">Start</string>
    <string name="quick_settings_screen_record_stop">Stop</string>
    <string name="quick_settings_secondary_label_until">Until %s</string>
    <string name="quick_settings_settings_label">Settings</string>
    <string name="quick_settings_tethering_label">Tethering</string>
    <string name="quick_settings_tiles">default</string>
    <string name="quick_settings_tiles_default">wifi,bt,dnd,flashlight,rotation,battery,cell,airplane,cast,screenrecord</string>
    <string name="quick_settings_tiles_retail_mode">night,dark,dnd,flashlight,rotation,location</string>
    <string name="quick_settings_tiles_stock">wifi,cell,battery,dnd,flashlight,rotation,bt,airplane,location,hotspot,inversion,saver,dark,work,cast,night,screenrecord,reverse</string>
    <string name="quick_settings_time_label">Time</string>
    <string name="quick_settings_ui_mode_night_label">Dark theme</string>
    <string name="quick_settings_user_label">Me</string>
    <string name="quick_settings_user_new_user">New user</string>
    <string name="quick_settings_user_title">User</string>
    <string name="quick_settings_wifi_detail_empty_text">No Wi-Fi networks available</string>
    <string name="quick_settings_wifi_label">Wi-Fi</string>
    <string name="quick_settings_wifi_no_network">No Network</string>
    <string name="quick_settings_wifi_not_connected">Not Connected</string>
    <string name="quick_settings_wifi_off_label">Wi-Fi Off</string>
    <string name="quick_settings_wifi_on_label">Wi-Fi On</string>
    <string name="quick_settings_wifi_secondary_label_transient">Turning on…</string>
    <string name="quick_settings_work_mode_label">Work profile</string>
    <string name="quick_step_accessibility_toggle_overview">Toggle Overview</string>
    <string name="r">R</string>
    <string name="rear_wiper">后雨刮刮刷</string>
    <string name="rear_wiper_clean">后雨刮洗涤</string>
    <string name="rearview_mirror_adjustment_content">请用方向盘右侧滚轮进行调整，15S未操作将自动退出</string>
    <string name="rearview_mirror_adjustment_title">后视镜调整</string>
    <string name="rearview_mirror_heating">后视镜/后挡加热</string>
    <string name="recents_quick_scrub_onboarding">Drag right to quickly switch apps</string>
    <string name="recents_swipe_up_onboarding">Swipe up to switch apps</string>
    <string name="recorder">行车记录仪</string>
    <string name="regrettable_lack_of_easter_egg">¯\\_(ツ)_/¯</string>
    <string name="remaining_length_format">%1$s left</string>
    <string name="remote_input_image_insertion_text">sent an image</string>
    <string name="remove_app">移除应用</string>
    <string name="remove_app_message">从副驾屏移除该应用后，中控屏中仍保留，可再次从中控屏添加</string>
    <string name="remove_from_settings">Remove from Settings</string>
    <string name="remove_from_settings_prompt">Remove System UI Tuner from Settings and stop using all of its features?</string>
    <string name="replace_current_app">点击替换当前应用</string>
    <string name="report_rejected_touch">Report rejected touch</string>
    <string name="reset">Reset</string>
    <string name="reset_dock_app">恢复默认布局</string>
    <string name="reset_failed">重置失败</string>
    <string name="reset_success">重置成功</string>
    <string name="restart_button_description">Tap to restart this app and go full screen.</string>
    <string name="retail_demo_reset_message">Enter password to perform factory reset in demo mode</string>
    <string name="retail_demo_reset_next">Next</string>
    <string name="retail_demo_reset_title">Password required</string>
    <string name="retry">重试</string>
    <string name="right">右</string>
    <string name="right_back">右后</string>
    <string name="right_icon">Right icon</string>
    <string name="right_keycode">Right keycode</string>
    <string name="right_nav_bar_button_type">Extra right button type</string>
    <string name="right_rearview_mirror">右后视镜</string>
    <string name="ring_toggle_title">Calls</string>
    <string name="running_foreground_services_msg">Tap for details on battery and data usage</string>
    <string name="running_foreground_services_title">Apps running in background</string>
    <string name="running_process_item_user_label">User: %1$s</string>
    <string name="runningservices_settings_summary">View and control currently running services</string>
    <string name="runningservices_settings_title">Running services</string>
    <string name="safe_please_stop_adjust">为了您的安全，⻋辆⾏驶过程中不可使⽤零重⼒座椅，请在P挡重试</string>
    <string name="save">Save</string>
    <string name="saved_network">Saved by %1$s</string>
    <string name="scene_mode_opening">%s打开中</string>
    <string name="scene_mode_opening_desc">正在调整：%s</string>
    <string name="screen_brightness">亮度</string>
    <string name="screen_pinning_can_open_other_apps">Pinned app may open other apps.</string>
    <string name="screen_pinning_description">This keeps it in view until you unpin. Touch &amp; hold Back and Overview to unpin.</string>
    <string name="screen_pinning_description_accessible">This keeps it in view until you unpin. Touch &amp; hold Overview to unpin.</string>
    <string name="screen_pinning_description_gestural">This keeps it in view until you unpin. Swipe up &amp; hold to unpin.</string>
    <string name="screen_pinning_description_recents_invisible">This keeps it in view until you unpin. Touch &amp; hold Back and Home to unpin.</string>
    <string name="screen_pinning_description_recents_invisible_accessible">This keeps it in view until you unpin. Touch &amp; hold Home to unpin.</string>
    <string name="screen_pinning_exit">App unpinned</string>
    <string name="screen_pinning_exposes_personal_data">Personal data may be accessible (such as contacts and email content).</string>
    <string name="screen_pinning_negative">No thanks</string>
    <string name="screen_pinning_positive">Got it</string>
    <string name="screen_pinning_start">App pinned</string>
    <string name="screen_pinning_title">App is pinned</string>
    <string name="screen_pinning_toast">To unpin this app, touch &amp; hold Back and Overview buttons</string>
    <string name="screen_pinning_toast_gesture_nav">To unpin this app, swipe up &amp; hold</string>
    <string name="screen_pinning_toast_recents_invisible">To unpin this app, touch &amp; hold Back and Home buttons</string>
    <string name="screen_rotate">屏幕角度</string>
    <string name="screen_rotate_adjust">屏幕角度调节</string>
    <string name="screen_zoom_summary_custom">Custom (%d)</string>
    <string name="screen_zoom_summary_default">Default</string>
    <string name="screen_zoom_summary_extremely_large">Largest</string>
    <string name="screen_zoom_summary_large">Large</string>
    <string name="screen_zoom_summary_small">Small</string>
    <string name="screen_zoom_summary_very_large">Larger</string>
    <string name="screenrecord_audio_label">Record audio</string>
    <string name="screenrecord_background_processing_label">Processing screen recording</string>
    <string name="screenrecord_cancel_label">Cancel</string>
    <string name="screenrecord_cancel_success">Screen recording canceled</string>
    <string name="screenrecord_channel_description">Ongoing notification for a screen record session</string>
    <string name="screenrecord_delete_error">Error deleting screen recording</string>
    <string name="screenrecord_description">While recording, Android System can capture any sensitive information that’s visible on your screen or played on your device. This includes passwords, payment info, photos, messages, and audio.</string>
    <string name="screenrecord_device_audio_and_mic_label">Device audio and microphone</string>
    <string name="screenrecord_device_audio_description">Sound from your device, like music, calls, and ringtones</string>
    <string name="screenrecord_device_audio_label">Device audio</string>
    <string name="screenrecord_mic_label">Microphone</string>
    <string name="screenrecord_name">Screen Recorder</string>
    <string name="screenrecord_ongoing_screen_and_audio">Recording screen and audio</string>
    <string name="screenrecord_ongoing_screen_only">Recording screen</string>
    <string name="screenrecord_pause_label">Pause</string>
    <string name="screenrecord_permission_error">Failed to get permissions</string>
    <string name="screenrecord_resume_label">Resume</string>
    <string name="screenrecord_save_message">Screen recording saved, tap to view</string>
    <string name="screenrecord_share_label">Share</string>
    <string name="screenrecord_start">Start</string>
    <string name="screenrecord_start_error">Error starting screen recording</string>
    <string name="screenrecord_start_label">Start Recording?</string>
    <string name="screenrecord_stop_label">Stop</string>
    <string name="screenrecord_stop_text">Tap to stop</string>
    <string name="screenrecord_taps_label">Show touches on screen</string>
    <string name="screenshot_dismiss_ui_description">Dismiss screenshot</string>
    <string name="screenshot_failed_title">Couldn\'t save screenshot</string>
    <string name="screenshot_failed_to_capture_text">Taking screenshots isn\'t allowed by the app or your organization</string>
    <string name="screenshot_failed_to_save_text">Can\'t save screenshot due to limited storage space</string>
    <string name="screenshot_failed_to_save_unknown_text">Try taking screenshot again</string>
    <string name="screenshot_preview_description">Screenshot preview</string>
    <string name="screenshot_saved_text">Tap to view your screenshot</string>
    <string name="screenshot_saved_title">Screenshot saved</string>
    <string name="screenshot_saving_ticker">Saving screenshot…</string>
    <string name="screenshot_saving_title">Saving screenshot…</string>
    <string name="search_contact">搜索</string>
    <string name="search_menu">Search settings</string>
    <string name="search_menu_title">Search</string>
    <string name="seat_adjust_error">座椅调节失败</string>
    <string name="seat_heating">座椅加热</string>
    <string name="seat_motion_cannot_operation">座椅运动中，暂不可操作</string>
    <string name="seat_ventilation">座椅通风</string>
    <string name="see_more_title">See more</string>
    <string name="select_application">Select application</string>
    <string name="select_logd_size_dialog_title">Select Logger sizes per log buffer</string>
    <string name="select_logd_size_title">Logger buffer sizes</string>
    <string name="select_logpersist_dialog_title">Select log buffers to store persistently on device</string>
    <string name="select_logpersist_title">Store logger data persistently on device</string>
    <string name="select_private_dns_configuration_dialog_title">Select Private DNS Mode</string>
    <string name="select_private_dns_configuration_title">Private DNS</string>
    <string name="select_usb_configuration_dialog_title">Select USB Configuration</string>
    <string name="select_usb_configuration_title">Select USB Configuration</string>
    <string name="select_webview_provider_dialog_title">Set WebView implementation</string>
    <string name="select_webview_provider_title">WebView implementation</string>
    <string name="select_webview_provider_toast_text">This choice is no longer valid. Try again.</string>
    <string name="sensitive">灵敏度</string>
    <string name="sensor_privacy_mode">Sensors off</string>
    <string name="sentry">哨塔模式</string>
    <string name="sentry_open_sure">同意并开启</string>
    <string name="sentry_open_tip">1.电量消耗：哨塔模式功能开启期间，摄像头、麦克风等零部件在车辆处于OFF挡时持续工作，检测车内及周边环境，会额外消耗电量，请关注续航里程。\n\n2.隐私信息：哨塔模式功能开启期间，会对车内外持续录像，请注意保护自己和他人隐私。\n\n3.传感器寿命：哨塔模式功能开启期间，对摄像头、麦克风等零部件寿命有一定影响。</string>
    <string name="sentry_open_title">哨塔模式开启提示</string>
    <string name="settings">设置</string>
    <string name="settings_package">com.android.settings</string>
    <string name="shared_data_delete_failure_text">There was an error deleting the shared data.</string>
    <string name="shared_data_no_accessors_dialog_text">There are no leases acquired for this shared data. Would you like to delete it?</string>
    <string name="shared_data_no_blobs_text">There is no shared data for this user.</string>
    <string name="shared_data_query_failure_text">There was an error fetching shared data. Try again.</string>
    <string name="shared_data_summary">View and modify shared data</string>
    <string name="shared_data_title">Shared data</string>
    <string name="show_all_anrs">Show background ANRs</string>
    <string name="show_all_anrs_summary">Display App Not Responding dialog for background apps</string>
    <string name="show_battery_percentage">Show embedded battery percentage</string>
    <string name="show_battery_percentage_summary">Show battery level percentage inside the status bar icon when not charging</string>
    <string name="show_brightness">Show brightness in Quick Settings</string>
    <string name="show_demo_mode">Show demo mode</string>
    <string name="show_hw_layers_updates">Show hardware layers updates</string>
    <string name="show_hw_layers_updates_summary">Flash hardware layers green when they update</string>
    <string name="show_hw_screen_updates">Show view updates</string>
    <string name="show_hw_screen_updates_summary">Flash views inside windows when drawn</string>
    <string name="show_non_rect_clip">Debug non-rectangular clip operations</string>
    <string name="show_notification_channel_warnings">Show notification channel warnings</string>
    <string name="show_notification_channel_warnings_summary">Displays on-screen warning when an app posts a notification without a valid channel</string>
    <string name="show_screen_updates">Show surface updates</string>
    <string name="show_screen_updates_summary">Flash entire window surfaces when they update</string>
    <string name="show_silently">Show notifications silently</string>
    <string name="show_touches">Show taps</string>
    <string name="show_touches_summary">Show visual feedback for taps</string>
    <string name="simulate_color_space">Simulate color space</string>
    <string name="skylight">后穹顶</string>
    <string name="slice_permission_allow">Allow</string>
    <string name="slice_permission_checkbox">Allow %1$s to show slices from any app</string>
    <string name="slice_permission_deny">Deny</string>
    <string name="slice_permission_text_1">- It can read information from %1$s</string>
    <string name="slice_permission_text_2">- It can take actions inside %1$s</string>
    <string name="slice_permission_title">Allow %1$s to show %2$s slices?</string>
    <string name="smart_refresh_layout_component_falsify" formatted="false">%s falsify area,\n Represents the height[%.1fdp] of drag at run time,\n It does not show anything.</string>
    <string name="smart_refresh_layout_content_empty">The content view in SmartRefreshLayout is empty. Do you forget to add it in xml layout file?</string>
    <string name="smart_refresh_layout_footer_failed">加载失败</string>
    <string name="smart_refresh_layout_footer_finish">加载完成</string>
    <string name="smart_refresh_layout_footer_loading">正在加载…</string>
    <string name="smart_refresh_layout_footer_nothing">没有更多数据了</string>
    <string name="smart_refresh_layout_footer_pulling">上拉加载更多</string>
    <string name="smart_refresh_layout_footer_refreshing">等待头部刷新完成…</string>
    <string name="smart_refresh_layout_footer_release">释放立即加载</string>
    <string name="snooze_undo">UNDO</string>
    <string name="snoozed_for_time">Snoozed for %1$s</string>
    <string name="snow">雪地</string>
    <string name="someone_back_have_person">执行中后排有人</string>
    <string name="speed">速度</string>
    <string name="speed_bump_explanation">Less urgent notifications below</string>
    <string name="speed_dissatisfaction_requirement">车速不满足</string>
    <string name="speed_label_fast">Fast</string>
    <string name="speed_label_medium">Medium</string>
    <string name="speed_label_okay">OK</string>
    <string name="speed_label_slow">Slow</string>
    <string name="speed_label_very_fast">Very Fast</string>
    <string name="speed_label_very_slow">Very Slow</string>
    <string name="sports">运动</string>
    <string name="sqlite_full">数据库或磁盘已满,请先进行清理</string>
    <string name="sqlite_save_error">数据库保存异常</string>
    <string name="srvm">后视辅助</string>
    <string name="ssl_ca_cert_warning">Network may\nbe monitored</string>
    <string name="standard">舒适</string>
    <string name="standby_bucket_summary">App standby state: %s</string>
    <string name="start_dreams">Screen saver</string>
    <string name="status_bar">Status bar</string>
    <string name="status_bar_airplane">Airplane mode</string>
    <string name="status_bar_alarm">Alarm</string>
    <string name="status_bar_clear_all_button">Clear</string>
    <string name="status_bar_ethernet">Ethernet</string>
    <string name="status_bar_input_method_settings_configure_input_methods">Set up input methods</string>
    <string name="status_bar_latest_events_title">Notifications</string>
    <string name="status_bar_network_name_separator">|</string>
    <string name="status_bar_no_notifications_title">No notifications</string>
    <string name="status_bar_notification_app_settings_title">%s settings</string>
    <string name="status_bar_notification_info_overflow">999+</string>
    <string name="status_bar_notification_inspect_item_title">Notification settings</string>
    <string name="status_bar_ongoing_events_title">Ongoing</string>
    <string name="status_bar_settings_auto_brightness_label">AUTO</string>
    <string name="status_bar_settings_auto_rotation">Auto-rotate screen</string>
    <string name="status_bar_settings_mute_label">MUTE</string>
    <string name="status_bar_settings_notifications">Notifications</string>
    <string name="status_bar_settings_settings_button">Settings</string>
    <string name="status_bar_settings_wifi_button">Wi-Fi</string>
    <string name="status_bar_use_physical_keyboard">Physical keyboard</string>
    <string name="status_bar_work">Work profile</string>
    <string name="status_unavailable">Unavailable</string>
    <string name="steering_assist_adjustment">转向助力调节</string>
    <string name="steering_wheel_adjustment">方向盘调节</string>
    <string name="steering_wheel_adjustment_content">请用方向盘右侧滚轮进行调整，8S未操作将自动退出</string>
    <string name="steering_wheel_adjustment_title">方向盘调节</string>
    <string name="steering_wheel_heating">方向盘加热</string>
    <string name="storage_category">Storage</string>
    <string name="str_adjust_seat">座椅调节</string>
    <string name="str_air_acAirOutMode">扫风模式</string>
    <string name="str_air_acAutoAirClean">空气净化</string>
    <string name="str_air_acCircleModeStatus">空气循环</string>
    <string name="str_air_accOnOff">空调开关</string>
    <string name="str_air_close">已关闭</string>
    <string name="str_air_coHotLevel">副驾加热</string>
    <string name="str_air_coWindLevel">副驾通风</string>
    <string name="str_air_frontAcMode">A/C</string>
    <string name="str_air_frontDefrostStatus">前除霜</string>
    <string name="str_air_frontDriverTempStatus">主驾驶温度</string>
    <string name="str_air_frontFanSpeed">空调风量</string>
    <string name="str_air_frontPassengerTempStatus">副驾驶温度</string>
    <string name="str_air_mainHotLevel">主驾加热</string>
    <string name="str_air_mainWindLevel">主驾通风</string>
    <string name="str_air_open">已开启</string>
    <string name="str_app_added">%sAPP已添加</string>
    <string name="str_app_name">相机</string>
    <string name="str_back_window_close_tip">后背窗关闭提示</string>
    <string name="str_back_window_tip">关闭时将联动关闭后背门和后穹顶，请注意周围是否有障碍物</string>
    <string name="str_before">刚刚</string>
    <string name="str_blue_connecting">%s正在连接，请稍后</string>
    <string name="str_blue_paring">已发送配对请求，请到“%s”确认</string>
    <string name="str_blue_support">支持蓝牙耳机连接</string>
    <string name="str_blue_title">蓝牙：</string>
    <string name="str_bluetooth">蓝牙</string>
    <string name="str_bluetooth_ignore">忽略此设备</string>
    <string name="str_bluetooth_setting">蓝牙设置</string>
    <string name="str_bluetooth_setting_content">请进行已配对蓝牙设置</string>
    <string name="str_camera_no_permission">摄像头未授权</string>
    <string name="str_camera_permission">摄像头已授权</string>
    <string name="str_cancel_top">取消置顶</string>
    <string name="str_cannot_fixed">默认数据不支持固定</string>
    <string name="str_clean_message">清空消息</string>
    <string name="str_click_sound">触控反馈音</string>
    <string name="str_close_co_screen">关闭副驾屏</string>
    <string name="str_closing_trunk">正在关闭</string>
    <string name="str_co_driver_mobile_control_tip">1、手机与车机网络连接\n2、打开「CD701副驾屏遥控器」小程序，扫一扫</string>
    <string name="str_co_driver_seat_belt">副驾请系好安全带</string>
    <string name="str_co_screen_adjust_angle">展开角度调节</string>
    <string name="str_confirm">点击确认</string>
    <string name="str_content_cannot_empty">内容不能为空</string>
    <string name="str_danger_abs">ABS故障,请尽快检查维修</string>
    <string name="str_danger_aided_driving_error">辅助驾驶系统受限</string>
    <string name="str_danger_anti_lock_system">制动防抱死系统故障请尽快检修</string>
    <string name="str_danger_auto_hold">Autohold激活失败</string>
    <string name="str_danger_battery_failure">蓄电池故障,请联系售后处理</string>
    <string name="str_danger_battery_failure_high">蓄电池电压过高,电源管理器已断开</string>
    <string name="str_danger_battery_failure_low">蓄电池电压过低,电源管理器已断开</string>
    <string name="str_danger_battery_failure_overload">蓄电池电压过载,电源管理器已断开</string>
    <string name="str_danger_brake_booster_system">制动助力系统异常,请谨慎驾驶</string>
    <string name="str_danger_brake_disc">制动盘温度过高,请谨慎驾驶</string>
    <string name="str_danger_brake_fluid">制动液不足请尽快检修</string>
    <string name="str_danger_braking_system">制动系统异常请尽快检修</string>
    <string name="str_danger_check_left_front_tyre_flat">左前轮胎漏气,请谨慎驾驶至维修点处理</string>
    <string name="str_danger_check_left_rear_tyre_flat">左后轮胎漏气,请谨慎驾驶至维修点处理</string>
    <string name="str_danger_check_right_front_tyre_flat">右前轮胎漏气,请谨慎驾驶至维修点处理</string>
    <string name="str_danger_check_right_rear_tyre_flat">右后轮胎漏气,请谨慎驾驶至维修点处理</string>
    <string name="str_danger_child_seat_belt">儿童座椅安全带未系好,请立即查看</string>
    <string name="str_danger_child_seat_install_error">儿童座椅安装错误,请重新安装</string>
    <string name="str_danger_coolant">冷却液不足请尽快检修</string>
    <string name="str_danger_discharging_function">无法对外供电系统故障</string>
    <string name="str_danger_ebd">EBD故障,请尽快检查维修</string>
    <string name="str_danger_eps">转向助力系统异常,请谨慎驾驶</string>
    <string name="str_danger_front_radar_exception">前雷达工作异常</string>
    <string name="str_danger_lane_assist">车道辅助系统故障,请至4S店检查维修</string>
    <string name="str_danger_low_l_e">左后轮胎压低,请参照用户手册补气</string>
    <string name="str_danger_low_l_s">左前轮胎压低,请参照用户手册补气</string>
    <string name="str_danger_low_r_e">右后轮胎压低,请参照用户手册补气</string>
    <string name="str_danger_low_r_s">右前轮胎压低,请参照用户手册补气</string>
    <string name="str_danger_power_manager_exception">电源管理器异常,电源管理器已断开</string>
    <string name="str_danger_power_manager_temperature_too_high">电源管理器温度过高,电源管理器已断开</string>
    <string name="str_danger_radar_exception">雷达工作异常</string>
    <string name="str_danger_rear_radar_exception">后雷达工作异常</string>
    <string name="str_danger_replace_left_front_tyre_flat">左前轮胎漏气,请靠边停车并更换备胎</string>
    <string name="str_danger_replace_left_rear_tyre_flat">左后轮胎漏气,请靠边停车并更换备胎</string>
    <string name="str_danger_replace_right_front_tyre_flat">右前轮胎漏气,请靠边停车并更换备胎</string>
    <string name="str_danger_replace_right_rear_tyre_flat">右后轮胎漏气,请靠边停车并更换备胎</string>
    <string name="str_danger_stable_system">车身电子稳定系统故障请尽快检修</string>
    <string name="str_danger_suspension_compressor_overheats">悬架压缩机过热</string>
    <string name="str_danger_suspension_p_error">P档异常，请安全停车，联系客服</string>
    <string name="str_danger_tire_pressure_low">胎压过低,请尽快检查轮胎</string>
    <string name="str_danger_tire_pressure_low_2_5">建议调整胎压至%sbar</string>
    <string name="str_danger_tire_pressure_sensor">胎压传感器信号异常,请尽快检查维修</string>
    <string name="str_danger_tire_pressure_sensor_battery_low">胎压传感器电量不足,请尽快检查维修</string>
    <string name="str_danger_tire_pressure_sensor_hardware">胎压传感器硬件故障,请尽快检查维修</string>
    <string name="str_danger_traction">牵引力控制系统故障,请尽快检查维修</string>
    <string name="str_danger_wireless_charge_work_status_error">无线充电检测到金属，请将金属异物移开</string>
    <string name="str_day_before">天前</string>
    <string name="str_delay_blocking">延迟闭锁</string>
    <string name="str_delay_blocking_tip">车辆将在5分钟内检测到主驾无人，并且在关好所有车门30秒后执行闭锁，是否立即执行？</string>
    <string name="str_dock_reset">已恢复默认布局</string>
    <string name="str_drag_location">( 拖动调整位置 )</string>
    <string name="str_dvr_sd_unmounted_button">知道了</string>
    <string name="str_dvr_sd_unmounted_content">无存储卡或存储卡不可用，建议插入容量为8~64G、速度CLASS10 (10MB/S)以上的存储卡。</string>
    <string name="str_dvr_sd_unmounted_title">存储卡不可用</string>
    <string name="str_energy_manager_mode">能量模式</string>
    <string name="str_energy_manager_mode_intelligence">AI智能</string>
    <string name="str_energy_manager_mode_intelligence_base">智能模式</string>
    <string name="str_energy_manager_mode_mixed">燃油优先</string>
    <string name="str_energy_manager_mode_oil_motor">油电混合</string>
    <string name="str_energy_manager_mode_pure_electric">Электрик</string>
    <string name="str_energy_manager_mode_range">增程电驱</string>
    <string name="str_evs_code_camp_battery_not_support">由于电量不满足，营地守护模式不可用</string>
    <string name="str_evs_code_camp_gear_is_not_p">由于当前处于非P挡，营地守护模式不可用</string>
    <string name="str_evs_code_camp_sdcard_not_support">由于SD卡状态不满足，营地守护模式不可用</string>
    <string name="str_evs_code_common_msg_no">开启失败，营地守护模式不可用</string>
    <string name="str_evs_code_common_msg_no_ok">开启失败，营地守护模式不可用</string>
    <string name="str_evs_code_common_msg_timeout">由于服务器处理超时，营地守护模式不可用</string>
    <string name="str_evs_code_common_msg_unhandled">由于服务器未处理，营地守护模式不可用</string>
    <string name="str_exchange">切换</string>
    <string name="str_eye_mode_close">已调节为标准色温</string>
    <string name="str_front_cabin_camping">座椅调节已锁定</string>
    <string name="str_front_cabin_camping_open">座椅位置已锁定，请前往露营模式解锁后重试</string>
    <string name="str_fuel_tank_cover">油箱盖</string>
    <string name="str_goto_4s">导航去 4S 店</string>
    <string name="str_goto_4s_keyword">长安4S店</string>
    <string name="str_jump_not_support">不支持的跳转</string>
    <string name="str_km">%s km</string>
    <string name="str_known_net">已知网络</string>
    <string name="str_light_auto">自动亮度</string>
    <string name="str_message_cloud">云端</string>
    <string name="str_message_system">系统</string>
    <string name="str_mic_in_ues">摄像头调用中</string>
    <string name="str_mic_in_use">麦克风调用中</string>
    <string name="str_mic_no_permission">麦克风未授权</string>
    <string name="str_mic_permission">麦克风已授权</string>
    <string name="str_minute_before">分钟前</string>
    <string name="str_mobile_control_wx">手机微信扫一扫，即可用手机控制副驾屏</string>
    <string name="str_my_device">我的设备</string>
    <string name="str_name_apa">智能泊车</string>
    <string name="str_name_blue_wifi_wifiap">WLAN/热点</string>
    <string name="str_name_camera">相机权限受限</string>
    <string name="str_name_capsule">通话胶囊</string>
    <string name="str_name_danger">报警/消息</string>
    <string name="str_name_evs">全景影像</string>
    <string name="str_name_game_console">游戏主机</string>
    <string name="str_name_mic">麦克风/摄像头权限</string>
    <string name="str_name_scene_mode">场景模式</string>
    <string name="str_name_user_center">用户中心</string>
    <string name="str_net">网络</string>
    <string name="str_no_message">暂无通知</string>
    <string name="str_open_setting_blue">打开蓝牙设置</string>
    <string name="str_open_setting_net">打开流量设置</string>
    <string name="str_open_setting_wifi">打开连接设置</string>
    <string name="str_open_setting_wifiap">打开连接设置</string>
    <string name="str_open_tip">开启提示</string>
    <string name="str_opening_trunk">正在打开</string>
    <string name="str_other_device">其他设备</string>
    <string name="str_other_net">其它网络</string>
    <string name="str_percent">%d%%</string>
    <string name="str_period_data_remain" formatted="false">剩余流量：%d%s</string>
    <string name="str_personal_wifi_ap">个人热点</string>
    <string name="str_please_choose_wallpaper_style">请选择壁纸风格</string>
    <string name="str_please_input_name">请输入名称</string>
    <string name="str_please_input_password">请输入密码</string>
    <string name="str_please_try_again_in_the_p_level">请在P挡下重试</string>
    <string name="str_please_turn_on_the_headlights_first">请先打开大灯</string>
    <string name="str_postion_no_permission">位置信息未授权</string>
    <string name="str_postion_permission">位置信息已调用</string>
    <string name="str_potision_in_ues">位置信息调用中</string>
    <string name="str_rear_defrost_mode">后除霜</string>
    <string name="str_rear_mirror_heating">后视镜加热</string>
    <string name="str_reboot_or_4s">您可以重启车机，或去附近的 4S 店进行维保</string>
    <string name="str_recorder_error">抱歉，行车记录仪出现故障了</string>
    <string name="str_screen_adjust_angle">展开角度调节</string>
    <string name="str_screen_clean">屏幕清洁模式</string>
    <string name="str_screen_projection">无线投屏</string>
    <string name="str_screen_sleep_time_10">10分钟</string>
    <string name="str_screen_sleep_time_3">3分钟</string>
    <string name="str_screen_sleep_time_5">5分钟</string>
    <string name="str_screen_sleep_time_no">不熄屏</string>
    <string name="str_scroll_to_right">向右滑动删除，仍可至应用内查看</string>
    <string name="str_set_top">置顶</string>
    <string name="str_setting_fail">设置失败</string>
    <string name="str_showroom_mode_not_support">出于安全考虑，在展厅模式下，部分功能有所受限，可进行试驾，获取更佳体验哦！</string>
    <string name="str_sunshade">遮阳帘</string>
    <string name="str_system_message">系统消息</string>
    <string name="str_system_service">系统服务</string>
    <string name="str_today">今天</string>
    <string name="str_trunk">后背门</string>
    <string name="str_visitor">游客</string>
    <string name="str_wallpaper_change_style">换⻛格</string>
    <string name="str_wallpaper_fixed">固定壁纸</string>
    <string name="str_wallpaper_fixed_cancel">取消固定</string>
    <string name="str_wallpaper_style">壁纸⻛格</string>
    <string name="str_warm_prompt">温馨提示</string>
    <string name="str_washing_mode_speed">当前车速&gt;15km/h，请在降低车速后重试</string>
    <string name="str_washing_mode_tip_1">1. 洗车模式将关闭部分车辆功能，以保证洗车时车辆安全</string>
    <string name="str_washing_mode_tip_2">2. 如使用传送带式自动洗车，请在车辆就位后挂入N挡</string>
    <string name="str_week">周</string>
    <string name="str_week2">星期</string>
    <string name="str_wifi">WLAN</string>
    <string name="str_wifi_ap">热点</string>
    <string name="str_wiper_sensitivity_1">1档</string>
    <string name="str_wiper_sensitivity_2">二档</string>
    <string name="str_wiper_sensitivity_3">三档</string>
    <string name="str_wiper_sensitivity_4">四档</string>
    <string name="str_wiper_sensitivity_5">五档</string>
    <string name="str_wireless">无线充电</string>
    <string name="str_yesterday">昨天</string>
    <string name="str_ymd_format">yyyy年MM月dd日</string>
    <string name="str_ymde_format">yyyy年MM月dd日 E</string>
    <string name="stream_accessibility">Accessibility</string>
    <string name="stream_alarm">Alarm</string>
    <string name="stream_bluetooth_sco">Bluetooth</string>
    <string name="stream_dtmf">Dual multi tone frequency</string>
    <string name="stream_music">Media</string>
    <string name="stream_notification">Notification</string>
    <string name="stream_ring">Ring</string>
    <string name="stream_system">System</string>
    <string name="stream_system_enforced">System enforced</string>
    <string name="stream_tts">Transmitted Through Speaker</string>
    <string name="stream_voice_call">Call</string>
    <string name="strict_mode">Strict mode enabled</string>
    <string name="strict_mode_summary">Flash screen when apps do long operations on main thread</string>
    <string name="string_360_forbid_option_tip">退出全景影像后可使用该功能</string>
    <string name="string_action_connect">连接</string>
    <string name="string_add_new_user">添加新用户</string>
    <string name="string_app_update_tip">应用更新中，请稍后再试</string>
    <string name="string_assistant">语音助手</string>
    <string name="string_auto_screen_sleep_time">自动熄屏时长</string>
    <string name="string_back_respect_pos">后排尊享</string>
    <string name="string_back_warehouse_tip">行驶过程中请勿开关后备舱</string>
    <string name="string_back_window_not">行车时打开后背窗有物品掉落风险,是否开启后背窗？</string>
    <string name="string_bluetooth_mix">设备连接数量已达上限，请断开1个设备后重试</string>
    <string name="string_bluetooth_peripheral_mix">已连接2个手柄，请断开一个手柄后重试</string>
    <string name="string_charge_close_tip">请拔出充/放电枪后重试</string>
    <string name="string_charge_open_tip">行驶过程中请勿开启充电口</string>
    <string name="string_charge_tip">充电口提示</string>
    <string name="string_charge_tip_desc">当前尾箱插座板已开启,打开充电口后车内插座将无法供电,确认是否开启充电口</string>
    <string name="string_cinema_mode">观影空间</string>
    <string name="string_cinema_mode_tip_close">退出提示</string>
    <string name="string_cinema_mode_tip_close_des">退出观影空间后，座椅将恢复「常用位置」，确定退出吗？</string>
    <string name="string_cinema_mode_tip_open">已为您打开「尊享副驾-观影空间」</string>
    <string name="string_clamour">报警音</string>
    <string name="string_close_all">一键全关</string>
    <string name="string_close_all_tip">一键全关提示</string>
    <string name="string_close_not">不断开</string>
    <string name="string_co_screen_remove">从副驾屏移除</string>
    <string name="string_co_seate_adjust">副驾座椅调节</string>
    <string name="string_common">常用</string>
    <string name="string_common_pos">常用位置</string>
    <string name="string_connect_hotspot">将手机连接至车机热点</string>
    <string name="string_drive">Режим 1</string>
    <string name="string_driving_mode_1">请关闭车门后再试</string>
    <string name="string_driving_mode_2">泊车智能辅助中，不支持</string>
    <string name="string_driving_mode_3">提示行车智能辅助驾驶中，不支持</string>
    <string name="string_driving_mode_4">车速大于30km/h,请降低车速后重试</string>
    <string name="string_empty_controller">没有更多组件可添加了</string>
    <string name="string_enter_user_center">账户设置</string>
    <string name="string_first_step">第一步</string>
    <string name="string_fuel_tank_cover_not">行驶过程中请勿操作油箱盖</string>
    <string name="string_fuel_tank_cover_yes">油箱盖解锁已完成</string>
    <string name="string_hicar_des">连接蓝牙设备将会断开当前HUAWEI HiCar连接，是否切换至蓝牙设备连接？</string>
    <string name="string_hicar_title">HUAWEI HiCar将断开连接</string>
    <string name="string_long_click_save">点击图标运行位置，长按卡片可保存当前位置数据</string>
    <string name="string_look_out">注意：可能存在视频APP兼容性问题导致投屏失败</string>
    <string name="string_main_seat">Сиденье водителя</string>
    <string name="string_make_up">化妆</string>
    <string name="string_makeup_space">化妆空间</string>
    <string name="string_media">媒体</string>
    <string name="string_miracast">无线投屏</string>
    <string name="string_navigation">导航</string>
    <string name="string_open_all">一键全开</string>
    <string name="string_open_all_tip">一键全开提示</string>
    <string name="string_passenger_seat">副驾位置</string>
    <string name="string_pause_tip">暂停</string>
    <string name="string_phone">通话</string>
    <string name="string_pickup_tip">拓展模式提示</string>
    <string name="string_power_off">关机</string>
    <string name="string_power_off_tip">Уведомление о выключении</string>
    <string name="string_power_off_tip_des">Выключить? Экран уберется.</string>
    <string name="string_r_gear_option_tip">退出R挡后可使用该功能</string>
    <string name="string_screed">熄屏</string>
    <string name="string_second_step">第二步</string>
    <string name="string_select_device">打开手机视频APP，点击TV，选择将要投屏的车辆</string>
    <string name="string_skylight_close">全关</string>
    <string name="string_skylight_not">行驶过程中请勿打开后穹顶</string>
    <string name="string_skylight_open">全开</string>
    <string name="string_skylight_open_process">打开%d%%</string>
    <string name="string_spare_pos">备用位置</string>
    <string name="string_standby">Режим 2</string>
    <string name="string_support_video">支持视频</string>
    <string name="string_support_video_apps">爱奇艺、腾讯视频、芒果TV、bilibili、优酷视频、咪咕视频等</string>
    <string name="string_sync">同步</string>
    <string name="string_trunk_close">确定关闭后背门吗？</string>
    <string name="string_trunk_not">行驶过程中请勿开关后背门</string>
    <string name="string_trunk_open">确定开启后背门吗？</string>
    <string name="string_updating">更新中</string>
    <string name="string_viewing">观影</string>
    <string name="string_warn_epb">电子驻车系统异常请谨慎驾驶</string>
    <string name="string_warn_higher_elc">高压系统异常 请联系专业人员检修</string>
    <string name="string_warn_lower_elc">低压供电异常 请尽快检修</string>
    <string name="string_warn_power">动力系统异常 请谨慎驾驶</string>
    <string name="string_warn_power_elc">发动机异常 仅纯电行驶</string>
    <string name="string_wiper_tip">雨刮处于维修位置,请关闭后重试！</string>
    <string name="string_zero_g">零重力</string>
    <string name="string_zero_gravity">零重力位置</string>
    <string name="summary_collapsed_preference_list">%1$s, %2$s</string>
    <string name="summary_empty" />
    <string name="summary_separator"> / </string>
    <string name="super_power_save_tip">已开启超级省电模式，该功能不可用</string>
    <string name="suspension_adjustment">悬架调节</string>
    <string name="suv_mode_ing">SUV模式运动中，模式打开后重试</string>
    <string name="switch_bar_off">Off</string>
    <string name="switch_bar_on">On</string>
    <string name="synchronization_phone">同步手机，打电话更自由</string>
    <string name="synchronization_phone_book">同步通讯录</string>
    <string name="synchronize_phone_book_error">同步通话记录出错，请重试</string>
    <string name="system_settings">系统设置</string>
    <string name="system_ui_aod_date_pattern">EEEMMMd</string>
    <string name="system_ui_date_pattern">@android:string/permlab_foregroundServiceCamera</string>
    <string name="system_ui_tuner">System UI Tuner</string>
    <string name="system_volume">音量设置</string>
    <string name="tap_to_renew_subscription_and_connect">Tap to renew subscription and connect</string>
    <string name="tap_to_sign_up">Tap to sign up</string>
    <string name="tether_settings_title_all">Tethering &amp; portable hotspot</string>
    <string name="tether_settings_title_bluetooth">Bluetooth tethering</string>
    <string name="tether_settings_title_usb">USB tethering</string>
    <string name="tether_settings_title_usb_bluetooth">Tethering</string>
    <string name="tether_settings_title_wifi">Portable hotspot</string>
    <string name="tethering_hardware_offload">Tethering hardware acceleration</string>
    <string name="tethering_hardware_offload_summary">Use tethering hardware acceleration if available</string>
    <string name="tethering_settings_not_available">Tethering settings are not available for this user</string>
    <string name="text_operation_failed">操作失败</string>
    <string name="text_operation_success">操作成功</string>
    <string name="text_save_failed_reset">位置保存失败，请重试</string>
    <string name="text_save_success">位置保存成功</string>
    <string name="text_seat_error">座椅故障</string>
    <string name="theme">Theme</string>
    <string name="themepicker_overlayable_package">com.android.wallpaper</string>
    <string name="thermal_shutdown_dialog_help_text">See care steps</string>
    <string name="thermal_shutdown_dialog_help_url" />
    <string name="thermal_shutdown_dialog_message">Your phone was too hot, so it turned off to cool down. Your phone is now running normally.\n\nYour phone may get too hot if you:\n\t• Use resource-intensive apps (such as gaming, video, or navigation apps)\n\t• Download or upload large files\n\t• Use your phone in high temperatures</string>
    <string name="thermal_shutdown_message">Your phone is now running normally.\nTap for more info</string>
    <string name="thermal_shutdown_title">Phone turned off due to heat</string>
    <string name="tile_unavailable">Unavailable</string>
    <string name="time_unit_just_now">Just now</string>
    <string name="tip_confirm_disconnect">是否断开「%s」连接</string>
    <string name="tip_confirm_disconnect_wifi">是否断开%s的网络连接</string>
    <string name="tip_connected">已连接</string>
    <string name="tip_connecting">连接中…</string>
    <string name="tip_connecting_guide">请通过电话确认配对</string>
    <string name="tip_device_change_connect" formatted="false">是否断开\"%s\"并连接\"%s\"</string>
    <string name="tip_device_disconnected">断开</string>
    <string name="tip_device_name">设备名称:</string>
    <string name="tip_disconnecting">断开中…</string>
    <string name="tip_discovering">发现</string>
    <string name="tip_force_p_gear">请在P挡下重试</string>
    <string name="tip_loading">加载中...</string>
    <string name="tip_no_call_log">无通话记录</string>
    <string name="tip_paired_device">已配对设备</string>
    <string name="tip_sync_contact">同步联系人</string>
    <string name="tip_sync_tts">输入TTS语音</string>
    <string name="tip_unknown_device">未知设备</string>
    <string name="tip_wifi_input_password">输入密码</string>
    <string name="tire_pressure_error_l_e">左后轮胎压异常,请尽快检修</string>
    <string name="tire_pressure_error_l_s">左前轮胎压异常,请尽快检修</string>
    <string name="tire_pressure_error_r_e">右后轮胎压异常,请尽快检修</string>
    <string name="tire_pressure_error_r_s">右前轮胎压异常,请尽快检修</string>
    <string name="title_convert_fbe">Converting to file based encryption</string>
    <string name="title_usb_accessory">USB accessory</string>
    <string name="toast_connect_bluetooth">请先连接蓝牙</string>
    <string name="touch_filtered_warning">Because an app is obscuring a permission request, Settings can’t verify your response.</string>
    <string name="track_frame_time">Profile HWUI rendering</string>
    <string name="transition_animation_scale_title">Transition animation scale</string>
    <string name="trunk">后背门</string>
    <string name="try_again">再试一次</string>
    <string name="tts_default_lang_summary">Sets the language-specific voice for the spoken text</string>
    <string name="tts_default_lang_title">Language</string>
    <string name="tts_default_pitch_summary">Affects the tone of the synthesized speech</string>
    <string name="tts_default_pitch_title">Pitch</string>
    <string name="tts_default_rate_summary">Speed at which the text is spoken</string>
    <string name="tts_default_rate_title">Speech rate</string>
    <string name="tts_default_sample_string">This is an example of speech synthesis</string>
    <string name="tts_engine_network_required">This language requires a working network connection for text-to-speech output.</string>
    <string name="tts_engine_preference_section_title">Preferred engine</string>
    <string name="tts_engine_security_warning">This speech synthesis engine may be able to collect all the text that will be spoken, including personal data like passwords and credit card numbers. It comes from the %s engine. Enable the use of this speech synthesis engine?</string>
    <string name="tts_engine_settings_button">Launch engine settings</string>
    <string name="tts_engine_settings_title">Settings for %s</string>
    <string name="tts_general_section_title">General</string>
    <string name="tts_install_data_summary">Install the voice data required for speech synthesis</string>
    <string name="tts_install_data_title">Install voice data</string>
    <string name="tts_lang_not_selected">Language not selected</string>
    <string name="tts_lang_use_system">Use system language</string>
    <string name="tts_play_example_summary">Play a short demonstration of speech synthesis</string>
    <string name="tts_play_example_title">Listen to an example</string>
    <string name="tts_reset_speech_pitch_summary">Reset the pitch at which the text is spoken to default.</string>
    <string name="tts_reset_speech_pitch_title">Reset speech pitch</string>
    <string name="tts_settings">Text-to-speech settings</string>
    <string name="tts_settings_title">Text-to-speech output</string>
    <string name="tts_status_checking">Checking…</string>
    <string name="tts_status_not_supported">%1$s is not supported</string>
    <string name="tts_status_ok">%1$s is fully supported</string>
    <string name="tts_status_requires_network">%1$s requires network connection</string>
    <string name="tts_status_title">Default language status</string>
    <string name="tuner_app">%1$s app</string>
    <string name="tuner_circle">Circle</string>
    <string name="tuner_doze">Ambient Display</string>
    <string name="tuner_doze_always_on">Always on</string>
    <string name="tuner_full_importance_settings">Power notification controls</string>
    <string name="tuner_full_importance_settings_off">Off</string>
    <string name="tuner_full_importance_settings_on">On</string>
    <string name="tuner_full_zen_title">Show with volume controls</string>
    <string name="tuner_launch_app">Launch %1$s</string>
    <string name="tuner_left">Left</string>
    <string name="tuner_lock_screen">Lock screen</string>
    <string name="tuner_low_priority">Show low-priority notification icons</string>
    <string name="tuner_menu">Menu</string>
    <string name="tuner_minus">Minus</string>
    <string name="tuner_other_apps">Other apps</string>
    <string name="tuner_persistent_warning">These experimental features may change, break, or disappear in future releases. Proceed with caution.</string>
    <string name="tuner_plus">Plus</string>
    <string name="tuner_right">Right</string>
    <string name="tuner_time">Time</string>
    <string name="tuner_toast">Congrats! System UI Tuner has been added to Settings</string>
    <string name="tuner_warning">System UI Tuner gives you extra ways to tweak and customize the Android user interface. These experimental features may change, break, or disappear in future releases. Proceed with caution.</string>
    <string name="tuner_warning_title">Fun for some but not for all</string>
    <string name="ui_mode">显示模式</string>
    <string name="unknown">未知</string>
    <string name="unloading_mode">轻松装卸</string>
    <string name="usb_accessory_confirm_prompt">Open %1$s to handle %2$s?</string>
    <string name="usb_accessory_permission_prompt">Allow %1$s to access %2$s?</string>
    <string name="usb_accessory_uri_prompt">No installed apps work with this USB accessory. Learn more about this accessory at %1$s</string>
    <string name="usb_audio_disable_routing">Disable USB audio routing</string>
    <string name="usb_audio_disable_routing_summary">Disable automatic routing to USB audio peripherals</string>
    <string name="usb_contaminant_message">To protect your device from liquid or debris, the USB port is disabled and won’t detect any accessories.\n\nYou’ll be notified when it’s okay to use the USB port again.</string>
    <string name="usb_contaminant_title">USB port disabled</string>
    <string name="usb_debugging_allow">Allow</string>
    <string name="usb_debugging_always">Always allow from this computer</string>
    <string name="usb_debugging_message">The computer\'s RSA key fingerprint is:\n%1$s</string>
    <string name="usb_debugging_secondary_user_message">The user currently signed in to this device can\'t turn on USB debugging. To use this feature, switch to the primary user.</string>
    <string name="usb_debugging_secondary_user_title">USB debugging not allowed</string>
    <string name="usb_debugging_title">Allow USB debugging?</string>
    <string name="usb_device_confirm_prompt">Open %1$s to handle %2$s?</string>
    <string name="usb_device_confirm_prompt_warn">Open %1$s to handle %2$s?\nThis app has not been granted record permission but could capture audio through this USB device.</string>
    <string name="usb_device_permission_prompt">Allow %1$s to access %2$s?</string>
    <string name="usb_device_permission_prompt_warn">Allow %1$s to access %2$s?\nThis app has not been granted record permission but could capture audio through this USB device.</string>
    <string name="usb_disable_contaminant_detection">Enable USB</string>
    <string name="usb_port_enabled">USB port enabled to detect chargers and accessories</string>
    <string name="usb_preference_title">USB file transfer options</string>
    <string name="use_mtp_button_title">Mount as a media player (MTP)</string>
    <string name="use_ptp_button_title">Mount as a camera (PTP)</string>
    <string name="use_system_language_to_select_input_method_subtypes">Use system languages</string>
    <string name="user_add_profile_item_summary">You can restrict access to apps and content from your account</string>
    <string name="user_add_profile_item_title">Restricted profile</string>
    <string name="user_add_user">Add user</string>
    <string name="user_add_user_item_summary">Users have their own apps and content</string>
    <string name="user_add_user_item_title">User</string>
    <string name="user_add_user_message_long">You can share this device with other people by creating additional users. Each user has their own space, which they can customize with apps, wallpaper, and so on. Users can also adjust device settings like Wi‑Fi that affect everyone.\n\nWhen you add a new user, that person needs to set up their space.\n\nAny user can update apps for all other users. Accessibility settings and services may not transfer to the new user.</string>
    <string name="user_add_user_message_short">When you add a new user, that person needs to set up their space.\n\nAny user can update apps for all other users.</string>
    <string name="user_add_user_title">Add new user?</string>
    <string name="user_add_user_type_title">Add</string>
    <string name="user_guest">Guest</string>
    <string name="user_info_settings_title">User info</string>
    <string name="user_limit_reached_title">User limit reached</string>
    <string name="user_logout_notification_action">LOGOUT USER</string>
    <string name="user_logout_notification_text">Logout current user</string>
    <string name="user_logout_notification_title">Logout user</string>
    <string name="user_need_lock_message">Before you can create a restricted profile, you’ll need to set up a screen lock to protect your apps and personal data.</string>
    <string name="user_new_profile_name">New profile</string>
    <string name="user_new_user_name">New user</string>
    <string name="user_remove_user_message">All apps and data of this user will be deleted.</string>
    <string name="user_remove_user_remove">Remove</string>
    <string name="user_remove_user_title">Remove user?</string>
    <string name="user_set_lock_button">Set lock</string>
    <string name="user_setup_button_setup_later">Not now</string>
    <string name="user_setup_button_setup_now">Set up now</string>
    <string name="user_setup_dialog_message">Make sure the person is available to take the device and set up their space</string>
    <string name="user_setup_dialog_title">Set up user now?</string>
    <string name="user_setup_profile_dialog_message">Set up profile now?</string>
    <string name="user_switch_to_user">Switch to %s</string>
    <string name="v7_preference_off">OFF</string>
    <string name="v7_preference_on">ON</string>
    <string name="velocity_tracker_impl">platform</string>
    <string name="verify_apps_over_usb_summary">Check apps installed via ADB/ADT for harmful behavior.</string>
    <string name="verify_apps_over_usb_title">Verify apps over USB</string>
    <string name="visitor_mode_dialog_action">去登录</string>
    <string name="visitor_mode_dialog_content">当前处于游客模式，无法操作编辑，请登录账号体验更多功能</string>
    <string name="visitor_mode_dialog_title">游客模式提醒</string>
    <string name="voice_assist_label">open voice assist</string>
    <string name="voice_hint">Swipe from icon for voice assist</string>
    <string name="volume">音量</string>
    <string name="volume_and_do_not_disturb">Do Not Disturb</string>
    <string name="volume_dialog_ringer_guidance_ring">Calls and notifications will ring (%1$s)</string>
    <string name="volume_dialog_title">%s volume controls</string>
    <string name="volume_dnd_silent">Volume buttons shortcut</string>
    <string name="volume_odi_captions_content_description">Captions overlay</string>
    <string name="volume_odi_captions_hint_disable">disable</string>
    <string name="volume_odi_captions_hint_enable">enable</string>
    <string name="volume_odi_captions_tip">Automatically caption media</string>
    <string name="volume_ringer_hint_mute">mute</string>
    <string name="volume_ringer_hint_unmute">unmute</string>
    <string name="volume_ringer_hint_vibrate">vibrate</string>
    <string name="volume_ringer_status_normal">Ring</string>
    <string name="volume_ringer_status_silent">Mute</string>
    <string name="volume_ringer_status_vibrate">Vibrate</string>
    <string name="volume_stream_content_description_mute">%1$s. Tap to mute. Accessibility services may be muted.</string>
    <string name="volume_stream_content_description_mute_a11y">%1$s. Tap to mute.</string>
    <string name="volume_stream_content_description_unmute">%1$s. Tap to unmute.</string>
    <string name="volume_stream_content_description_vibrate">%1$s. Tap to set to vibrate. Accessibility services may be muted.</string>
    <string name="volume_stream_content_description_vibrate_a11y">%1$s. Tap to set to vibrate.</string>
    <string name="volume_stream_limited_dnd">%s — Priority only</string>
    <string name="volume_stream_muted">%s silent</string>
    <string name="volume_stream_muted_dnd">%s silent — Total silence</string>
    <string name="volume_stream_suppressed">%1$s silent — %2$s</string>
    <string name="volume_stream_vibrate">%s vibrate</string>
    <string name="volume_stream_vibrate_dnd">%s vibrate — Priority only</string>
    <string name="volume_up_silent">Exit Do Not Disturb on volume up</string>
    <string name="volume_zen_end_now">Turn off now</string>
    <string name="vpn_footer">Network may be monitored</string>
    <string name="vpn_settings_not_available">VPN settings are not available for this user</string>
    <string name="wait_for_debugger">Wait for debugger</string>
    <string name="wait_for_debugger_summary">Debugged application waits for debugger to attach before executing</string>
    <string name="warehouse">后备舱</string>
    <string name="warn_close_contact">如果关闭此功能，您将无法使用通讯簿、通话记录等。是否确实要关闭此功能?</string>
    <string name="washing">洗车模式</string>
    <string name="wifi_ap_unable_to_handle_new_sta">Access point temporarily full</string>
    <string name="wifi_cant_connect">Can\'t connect</string>
    <string name="wifi_cant_connect_to_ap">Can\'t connect to \'%1$s\'</string>
    <string name="wifi_check_password_try_again">Check password and try again</string>
    <string name="wifi_connected_low_quality">Low quality</string>
    <string name="wifi_connected_no_internet">No internet</string>
    <string name="wifi_debugging_allow">Allow</string>
    <string name="wifi_debugging_always">Always allow on this network</string>
    <string name="wifi_debugging_message">Network Name (SSID)\n%1$s\n\nWi‑Fi Address (BSSID)\n%2$s</string>
    <string name="wifi_debugging_secondary_user_message">The user currently signed in to this device can’t turn on wireless debugging. To use this feature, switch to the primary user.</string>
    <string name="wifi_debugging_secondary_user_title">Wireless debugging not allowed</string>
    <string name="wifi_debugging_title">Allow wireless debugging on this network?</string>
    <string name="wifi_disabled_by_recommendation_provider">Not connected due to low quality network</string>
    <string name="wifi_disabled_generic">Disabled</string>
    <string name="wifi_disabled_network_failure">IP Configuration Failure</string>
    <string name="wifi_disabled_password_failure">Authentication problem</string>
    <string name="wifi_disabled_wifi_failure">WiFi Connection Failure</string>
    <string name="wifi_disconnected">Disconnected</string>
    <string name="wifi_display_certification">Wireless display certification</string>
    <string name="wifi_display_certification_summary">Show options for wireless display certification</string>
    <string name="wifi_dns1_hint">*******</string>
    <string name="wifi_dns2_hint">*******</string>
    <string name="wifi_enhanced_mac_randomization">Wi‑Fi‑enhanced MAC randomization</string>
    <string name="wifi_enhanced_mac_randomization_summary">When this mode is enabled, this device’s MAC address may change each time it connects to a network that has MAC randomization enabled.</string>
    <string name="wifi_fail_to_scan">Can\'t scan for networks</string>
    <string name="wifi_gateway_hint">***********</string>
    <string name="wifi_ip_address_hint">***********28</string>
    <string name="wifi_is_off">Wi-Fi is off</string>
    <string name="wifi_limited_connection">Limited connection</string>
    <string name="wifi_metered_label">Metered</string>
    <string name="wifi_network_prefix_length_hint">24</string>
    <string name="wifi_no_internet">No internet access</string>
    <string name="wifi_no_internet_no_reconnect">Won\'t automatically connect</string>
    <string name="wifi_not_in_range">Not in range</string>
    <string name="wifi_passpoint_expired">Expired</string>
    <string name="wifi_remembered">Saved</string>
    <string name="wifi_scan_throttling">Wi‑Fi scan throttling</string>
    <string name="wifi_scan_throttling_summary">Reduces battery drain &amp; improves network performance</string>
    <string name="wifi_security_eap">WPA/WPA2/WPA3-Enterprise</string>
    <string name="wifi_security_eap_suiteb">WPA3-Enterprise 192-bit</string>
    <string name="wifi_security_eap_wpa">WPA-Enterprise</string>
    <string name="wifi_security_eap_wpa2_wpa3">WPA2/WPA3-Enterprise</string>
    <string name="wifi_security_none">None</string>
    <string name="wifi_security_none_owe">None/Enhanced Open</string>
    <string name="wifi_security_owe">Enhanced Open</string>
    <string name="wifi_security_passpoint">Passpoint</string>
    <string name="wifi_security_psk_generic">@string/wifi_security_wpa_wpa2</string>
    <string name="wifi_security_psk_sae">WPA2/WPA3-Personal</string>
    <string name="wifi_security_sae">WPA3-Personal</string>
    <string name="wifi_security_short_eap">802.1x</string>
    <string name="wifi_security_short_eap_suiteb">Suite-B-192</string>
    <string name="wifi_security_short_eap_wpa">WPA-EAP</string>
    <string name="wifi_security_short_eap_wpa2_wpa3">RSN-EAP</string>
    <string name="wifi_security_short_none_owe">None/OWE</string>
    <string name="wifi_security_short_owe">OWE</string>
    <string name="wifi_security_short_psk_generic">@string/wifi_security_short_wpa_wpa2</string>
    <string name="wifi_security_short_psk_sae">WPA2/WPA3</string>
    <string name="wifi_security_short_sae">WPA3</string>
    <string name="wifi_security_short_wep">WEP</string>
    <string name="wifi_security_short_wpa">WPA</string>
    <string name="wifi_security_short_wpa2">WPA2</string>
    <string name="wifi_security_short_wpa2_wpa3">WPA2/WPA3</string>
    <string name="wifi_security_short_wpa_wpa2">WPA/WPA2</string>
    <string name="wifi_security_short_wpa_wpa2_wpa3">WPA/WPA2/WPA3</string>
    <string name="wifi_security_wep">WEP</string>
    <string name="wifi_security_wpa">WPA-Personal</string>
    <string name="wifi_security_wpa2">WPA2-Personal</string>
    <string name="wifi_security_wpa2_wpa3">WPA2/WPA3-Personal</string>
    <string name="wifi_security_wpa_wpa2">WPA/WPA2-Personal</string>
    <string name="wifi_security_wpa_wpa2_wpa3">WPA/WPA2/WPA3-Personal</string>
    <string name="wifi_status_mac_randomized">MAC is randomized</string>
    <string name="wifi_status_no_internet">No internet</string>
    <string name="wifi_status_sign_in_required">Sign in required</string>
    <string name="wifi_unmetered_label">Unmetered</string>
    <string name="wifi_verbose_logging">Enable Wi‑Fi Verbose Logging</string>
    <string name="wifi_verbose_logging_summary">Increase Wi‑Fi logging level, show per SSID RSSI in Wi‑Fi Picker</string>
    <string name="window_animation_scale_title">Window animation scale</string>
    <string name="window_lock">车窗锁</string>
    <string name="wiper_off">前挡风雨刮已关闭</string>
    <string name="wlan_connect_fail">WIFI连接失败</string>
    <string name="wlan_connect_password_error">密码输⼊错误，请重试</string>
    <string name="wlan_connect_success">WIFI连接成功</string>
    <string name="zen_alarm_warning">You won\'t hear your next alarm %1$s</string>
    <string name="zen_alarm_warning_indef">You won\'t hear your next alarm %1$s unless you turn this off before then</string>
    <string name="zen_alarms_introduction">You won\'t be disturbed by sounds and vibrations, except from alarms. You\'ll still hear anything you choose to play including music, videos, and games.</string>
    <string name="zen_interruption_level_priority">Priority only</string>
    <string name="zen_mode_and_condition">%1$s. %2$s</string>
    <string name="zen_mode_duration_always_prompt_title">Ask every time</string>
    <string name="zen_mode_duration_settings_title">Duration</string>
    <string name="zen_mode_enable_dialog_turn_on">Turn on</string>
    <string name="zen_mode_forever">Until you turn off</string>
    <string name="zen_mode_settings_summary_off">Never</string>
    <string name="zen_mode_settings_turn_on_dialog_title">Turn on Do Not Disturb</string>
    <string name="zen_priority_customize_button">Customize</string>
    <string name="zen_priority_introduction">You won\'t be disturbed by sounds and vibrations, except from alarms, reminders, events, and callers you specify. You\'ll still hear anything you choose to play including music, videos, and games.</string>
    <string name="zen_silence_introduction">This blocks ALL sounds and vibrations, including from alarms, music, videos, and games.</string>
    <string name="zen_silence_introduction_voice">This blocks ALL sounds and vibrations, including from alarms, music, videos, and games. You\'ll still be able to make phone calls.</string>
    <string name="zero_gravity_explain_co">零重力位置</string>
    <string name="zero_gravity_explain_co_desc">· 零重力座椅位置运动幅度较大，请在后方无人或贵重物品及静止状态下使\n用零重力座椅模式 \n· 在行驶过程中，零重力座椅位置靠背过于向后领斜，会严重影响安全带及\n安全气囊的保护，请勿在行驶过程中使用</string>
    <string name="zero_gravity_explain_co_reset">重置提示</string>
    <string name="zero_gravity_explain_co_reset_desc">确定重置零重力位置恢复为初始状态？</string>
    <string name="zero_gravity_gear_p">为了您的安全，请在P挡下使用零重力座椅</string>
</resources>
