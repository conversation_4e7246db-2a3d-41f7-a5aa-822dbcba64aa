package com.android.systemui.dump;

import android.content.Context;
import android.os.SystemClock;
import android.os.Trace;
import androidx.exifinterface.media.ExifInterface;
import com.android.systemui.R;
import com.incall.apps.casdkmanager.constants.CtNetAuthor;
import java.io.FileDescriptor;
import java.io.PrintWriter;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Logger;
import javax.inject.Inject;
import kotlin.Metadata;
import kotlin.collections.ArraysKt;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.StringsKt;
import sun.util.locale.LanguageTag;

/* compiled from: DumpHandler.kt */
@Metadata(d1 = {"\u0000h\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0011\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0005\n\u0002\u0010)\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\u0018\u0000 02\u00020\u0001:\u00010B\u001f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007¢\u0006\u0002\u0010\bJ)\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00110\u0010¢\u0006\u0002\u0010\u0012J\u0018\u0010\u0013\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0014H\u0002J\u0010\u0010\u0015\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000eH\u0002J \u0010\u0016\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0014H\u0002J \u0010\u0017\u001a\u00020\n2\u0006\u0010\u0018\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0014H\u0002J\u0010\u0010\u0019\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\u0018\u0010\u001a\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0014H\u0002J \u0010\u001b\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0014H\u0002J \u0010\u001c\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u001d\u001a\u00020\u00112\u0006\u0010\u001e\u001a\u00020\u001fH\u0002J.\u0010 \u001a\u00020\n2\f\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00110\"2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0014H\u0002J\u001b\u0010#\u001a\u00020\u00142\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00110\u0010H\u0002¢\u0006\u0002\u0010$JL\u0010%\u001a\u0002H&\"\u0004\b\u0000\u0010&2\f\u0010'\u001a\b\u0012\u0004\u0012\u00020\u00110(2\u0006\u0010)\u001a\u00020\u00112!\u0010*\u001a\u001d\u0012\u0013\u0012\u00110\u0011¢\u0006\f\b,\u0012\b\b-\u0012\u0004\b\b(.\u0012\u0004\u0012\u0002H&0+H\u0002¢\u0006\u0002\u0010/R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004¢\u0006\u0002\n\u0000¨\u00061"}, d2 = {"Lcom/android/systemui/dump/DumpHandler;", "", "context", "Landroid/content/Context;", "dumpManager", "Lcom/android/systemui/dump/DumpManager;", "logBufferEulogizer", "Lcom/android/systemui/dump/LogBufferEulogizer;", "(Landroid/content/Context;Lcom/android/systemui/dump/DumpManager;Lcom/android/systemui/dump/LogBufferEulogizer;)V", "dump", "", "fd", "Ljava/io/FileDescriptor;", "pw", "Ljava/io/PrintWriter;", "args", "", "", "(Ljava/io/FileDescriptor;Ljava/io/PrintWriter;[Ljava/lang/String;)V", "dumpBuffers", "Lcom/android/systemui/dump/ParsedArgs;", "dumpConfig", "dumpCritical", "dumpDumpables", "fw", "dumpHelp", "dumpNormal", "dumpParameterized", "dumpServiceList", "type", "resId", "", "dumpTargets", "targets", "", "parseArgs", "([Ljava/lang/String;)Lcom/android/systemui/dump/ParsedArgs;", "readArgument", ExifInterface.GPS_DIRECTION_TRUE, "iterator", "", "flag", "parser", "Lkotlin/Function1;", "Lkotlin/ParameterName;", "name", "arg", "(Ljava/util/Iterator;Ljava/lang/String;Lkotlin/jvm/functions/Function1;)Ljava/lang/Object;", "Companion", "app_release"}, k = 1, mv = {1, 5, 1}, xi = 48)
/* loaded from: classes2.dex */
public final class DumpHandler {
    public static final String PRIORITY_ARG = "--dump-priority";
    public static final String PRIORITY_ARG_CRITICAL = "CRITICAL";
    public static final String PRIORITY_ARG_HIGH = "HIGH";
    public static final String PRIORITY_ARG_NORMAL = "NORMAL";
    private final Context context;
    private final DumpManager dumpManager;
    private final LogBufferEulogizer logBufferEulogizer;

    @Inject
    public DumpHandler(Context context, DumpManager dumpManager, LogBufferEulogizer logBufferEulogizer) {
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(dumpManager, "dumpManager");
        Intrinsics.checkNotNullParameter(logBufferEulogizer, "logBufferEulogizer");
        this.context = context;
        this.dumpManager = dumpManager;
        this.logBufferEulogizer = logBufferEulogizer;
    }

    public final void dump(FileDescriptor fd, PrintWriter pw, String[] args) {
        Intrinsics.checkNotNullParameter(fd, "fd");
        Intrinsics.checkNotNullParameter(pw, "pw");
        Intrinsics.checkNotNullParameter(args, "args");
        Trace.beginSection("DumpManager#dump()");
        long uptimeMillis = SystemClock.uptimeMillis();
        try {
            ParsedArgs parseArgs = parseArgs(args);
            String dumpPriority = parseArgs.getDumpPriority();
            if (Intrinsics.areEqual(dumpPriority, PRIORITY_ARG_CRITICAL)) {
                dumpCritical(fd, pw, parseArgs);
            } else if (Intrinsics.areEqual(dumpPriority, PRIORITY_ARG_NORMAL)) {
                dumpNormal(pw, parseArgs);
            } else {
                dumpParameterized(fd, pw, parseArgs);
            }
            pw.println();
            pw.println("Dump took " + (SystemClock.uptimeMillis() - uptimeMillis) + "ms");
            Trace.endSection();
        } catch (ArgParseException e) {
            pw.println(e.getMessage());
        }
    }

    /* JADX WARN: Failed to restore switch over string. Please report as a decompilation issue
    java.lang.NullPointerException: Cannot invoke "java.util.List.iterator()" because the return value of "jadx.core.dex.visitors.regions.SwitchOverStringVisitor$SwitchData.getNewCases()" is null
    	at jadx.core.dex.visitors.regions.SwitchOverStringVisitor.restoreSwitchOverString(SwitchOverStringVisitor.java:109)
    	at jadx.core.dex.visitors.regions.SwitchOverStringVisitor.visitRegion(SwitchOverStringVisitor.java:66)
    	at jadx.core.dex.visitors.regions.DepthRegionTraversal.traverseIterativeStepInternal(DepthRegionTraversal.java:77)
    	at jadx.core.dex.visitors.regions.DepthRegionTraversal.traverseIterativeStepInternal(DepthRegionTraversal.java:82)
     */
    private final void dumpParameterized(FileDescriptor fd, PrintWriter pw, ParsedArgs args) {
        String command = args.getCommand();
        if (command != null) {
            switch (command.hashCode()) {
                case -1354792126:
                    if (command.equals(CtNetAuthor.KEY_CONFIG)) {
                        dumpConfig(pw);
                        return;
                    }
                    break;
                case -1353714459:
                    if (command.equals("dumpables")) {
                        dumpDumpables(fd, pw, args);
                        return;
                    }
                    break;
                case -1045369428:
                    if (command.equals("bugreport-normal")) {
                        dumpNormal(pw, args);
                        return;
                    }
                    break;
                case 3198785:
                    if (command.equals("help")) {
                        dumpHelp(pw);
                        return;
                    }
                    break;
                case 227996723:
                    if (command.equals("buffers")) {
                        dumpBuffers(pw, args);
                        return;
                    }
                    break;
                case 842828580:
                    if (command.equals("bugreport-critical")) {
                        dumpCritical(fd, pw, args);
                        return;
                    }
                    break;
            }
        }
        dumpTargets(args.getNonFlagArgs(), fd, pw, args);
    }

    private final void dumpCritical(FileDescriptor fd, PrintWriter pw, ParsedArgs args) {
        this.dumpManager.dumpDumpables(fd, pw, args.getRawArgs());
        dumpConfig(pw);
    }

    private final void dumpNormal(PrintWriter pw, ParsedArgs args) {
        this.dumpManager.dumpBuffers(pw, args.getTailLength());
        this.logBufferEulogizer.readEulogyIfPresent(pw);
    }

    private final void dumpDumpables(FileDescriptor fw, PrintWriter pw, ParsedArgs args) {
        if (args.getListOnly()) {
            this.dumpManager.listDumpables(pw);
        } else {
            this.dumpManager.dumpDumpables(fw, pw, args.getRawArgs());
        }
    }

    private final void dumpBuffers(PrintWriter pw, ParsedArgs args) {
        if (args.getListOnly()) {
            this.dumpManager.listBuffers(pw);
        } else {
            this.dumpManager.dumpBuffers(pw, args.getTailLength());
        }
    }

    private final void dumpTargets(List<String> targets, FileDescriptor fd, PrintWriter pw, ParsedArgs args) {
        if (!targets.isEmpty()) {
            Iterator<String> iterator2 = targets.iterator2();
            while (iterator2.getHasNext()) {
                this.dumpManager.dumpTarget(iterator2.next(), fd, pw, args.getRawArgs(), args.getTailLength());
            }
            return;
        }
        if (args.getListOnly()) {
            pw.println("Dumpables:");
            this.dumpManager.listDumpables(pw);
            pw.println();
            pw.println("Buffers:");
            this.dumpManager.listBuffers(pw);
            return;
        }
        pw.println("Nothing to dump :(");
    }

    private final void dumpConfig(PrintWriter pw) {
        pw.println("SystemUiServiceComponents configuration:");
        pw.print("vendor component: ");
        pw.println(this.context.getResources().getString(R.string.config_systemUIVendorServiceComponent));
        dumpServiceList(pw, Logger.GLOBAL_LOGGER_NAME, R.array.config_systemUIServiceComponents);
        dumpServiceList(pw, "per-user", R.array.config_systemUIServiceComponentsPerUser);
    }

    private final void dumpServiceList(PrintWriter pw, String type, int resId) {
        String[] stringArray = this.context.getResources().getStringArray(resId);
        pw.print(type);
        pw.print(": ");
        if (stringArray == null) {
            pw.println("N/A");
            return;
        }
        pw.print(stringArray.length);
        pw.println(" services");
        int length = stringArray.length;
        for (int i = 0; i < length; i++) {
            pw.print("  ");
            pw.print(i);
            pw.print(": ");
            pw.println(stringArray[i]);
        }
    }

    private final void dumpHelp(PrintWriter pw) {
        pw.println("Let <invocation> be:");
        pw.println("$ adb shell dumpsys activity service com.android.systemui/.SystemUIService");
        pw.println();
        pw.println("Most common usage:");
        pw.println("$ <invocation> <targets>");
        pw.println("$ <invocation> NotifLog");
        pw.println("$ <invocation> StatusBar FalsingManager BootCompleteCacheImpl");
        pw.println("etc.");
        pw.println();
        pw.println("Special commands:");
        pw.println("$ <invocation> dumpables");
        pw.println("$ <invocation> buffers");
        pw.println("$ <invocation> bugreport-critical");
        pw.println("$ <invocation> bugreport-normal");
        pw.println();
        pw.println("Targets can be listed:");
        pw.println("$ <invocation> --list");
        pw.println("$ <invocation> dumpables --list");
        pw.println("$ <invocation> buffers --list");
        pw.println();
        pw.println("Show only the most recent N lines of buffers");
        pw.println("$ <invocation> NotifLog --tail 30");
    }

    private final ParsedArgs parseArgs(String[] args) {
        String[] strArr;
        List mutableList = ArraysKt.toMutableList(args);
        ParsedArgs parsedArgs = new ParsedArgs(args, mutableList);
        Iterator<String> iterator2 = mutableList.iterator2();
        while (iterator2.getHasNext()) {
            String next = iterator2.next();
            if (StringsKt.startsWith$default(next, LanguageTag.SEP, false, 2, (Object) null)) {
                iterator2.remove();
                switch (next.hashCode()) {
                    case 1499:
                        if (!next.equals("-h")) {
                            throw new ArgParseException(Intrinsics.stringPlus("Unknown flag: ", next));
                        }
                        parsedArgs.setCommand("help");
                        break;
                    case 1503:
                        if (!next.equals("-l")) {
                            throw new ArgParseException(Intrinsics.stringPlus("Unknown flag: ", next));
                        }
                        parsedArgs.setListOnly(true);
                        break;
                    case 1511:
                        if (!next.equals("-t")) {
                            throw new ArgParseException(Intrinsics.stringPlus("Unknown flag: ", next));
                        }
                        parsedArgs.setTailLength(((Number) readArgument(iterator2, next, new Function1<String, Integer>() { // from class: com.android.systemui.dump.DumpHandler$parseArgs$2
                            @Override // kotlin.jvm.functions.Function1
                            public final Integer invoke(String it) {
                                Intrinsics.checkNotNullParameter(it, "it");
                                return Integer.valueOf(Integer.parseInt(it));
                            }
                        })).intValue());
                        break;
                    case 1056887741:
                        if (!next.equals(PRIORITY_ARG)) {
                            throw new ArgParseException(Intrinsics.stringPlus("Unknown flag: ", next));
                        }
                        parsedArgs.setDumpPriority((String) readArgument(iterator2, PRIORITY_ARG, new Function1<String, String>() { // from class: com.android.systemui.dump.DumpHandler$parseArgs$1
                            @Override // kotlin.jvm.functions.Function1
                            public final String invoke(String it) {
                                String[] strArr2;
                                Intrinsics.checkNotNullParameter(it, "it");
                                strArr2 = DumpHandlerKt.PRIORITY_OPTIONS;
                                if (ArraysKt.contains(strArr2, it)) {
                                    return it;
                                }
                                throw new IllegalArgumentException();
                            }
                        }));
                        break;
                    case 1333069025:
                        if (!next.equals("--help")) {
                            throw new ArgParseException(Intrinsics.stringPlus("Unknown flag: ", next));
                        }
                        parsedArgs.setCommand("help");
                        break;
                    case 1333192254:
                        if (!next.equals("--list")) {
                            throw new ArgParseException(Intrinsics.stringPlus("Unknown flag: ", next));
                        }
                        parsedArgs.setListOnly(true);
                        break;
                    case 1333422576:
                        if (!next.equals("--tail")) {
                            throw new ArgParseException(Intrinsics.stringPlus("Unknown flag: ", next));
                        }
                        parsedArgs.setTailLength(((Number) readArgument(iterator2, next, new Function1<String, Integer>() { // from class: com.android.systemui.dump.DumpHandler$parseArgs$2
                            @Override // kotlin.jvm.functions.Function1
                            public final Integer invoke(String it) {
                                Intrinsics.checkNotNullParameter(it, "it");
                                return Integer.valueOf(Integer.parseInt(it));
                            }
                        })).intValue());
                        break;
                    default:
                        throw new ArgParseException(Intrinsics.stringPlus("Unknown flag: ", next));
                }
            }
        }
        if (parsedArgs.getCommand() == null && (!mutableList.isEmpty())) {
            strArr = DumpHandlerKt.COMMANDS;
            if (ArraysKt.contains(strArr, mutableList.get(0))) {
                parsedArgs.setCommand((String) mutableList.remove(0));
            }
        }
        return parsedArgs;
    }

    private final <T> T readArgument(Iterator<String> iterator, String flag, Function1<? super String, ? extends T> parser) {
        if (!iterator.getHasNext()) {
            throw new ArgParseException(Intrinsics.stringPlus("Missing argument for ", flag));
        }
        String next = iterator.next();
        try {
            T invoke = parser.invoke(next);
            iterator.remove();
            return invoke;
        } catch (Exception unused) {
            throw new ArgParseException("Invalid argument '" + next + "' for flag " + flag);
        }
    }
}
