package com.android.systemui.util.animation;

import android.os.Looper;
import android.util.ArrayMap;
import android.util.Log;
import androidx.dynamicanimation.animation.AnimationHandler;
import androidx.dynamicanimation.animation.DynamicAnimation;
import androidx.dynamicanimation.animation.FlingAnimation;
import androidx.dynamicanimation.animation.FloatPropertyCompat;
import androidx.dynamicanimation.animation.SpringAnimation;
import androidx.dynamicanimation.animation.SpringForce;
import androidx.exifinterface.media.ExifInterface;
import com.android.systemui.util.animation.PhysicsAnimator;
import com.incall.apps.casdkmanager.constants.CtNetAuthor;
import com.incall.apps.constant.HMIDataConstant;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import kotlin.Metadata;
import kotlin.Unit;
import kotlin.collections.ArraysKt;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: PhysicsAnimator.kt */
@Metadata(d1 = {"\u0000\u009e\u0001\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\"\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0011\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0007\n\u0002\b\u001f\n\u0002\u0018\u0002\n\u0002\b\t\u0018\u0000 i*\u0004\b\u0000\u0010\u00012\u00020\u0002:\u0007hijklmnB\u000f\b\u0002\u0012\u0006\u0010\u0003\u001a\u00028\u0000¢\u0006\u0002\u0010\u0004J\u001a\u00104\u001a\b\u0012\u0004\u0012\u00028\u00000\u00002\f\u00105\u001a\b\u0012\u0004\u0012\u00028\u00000\u001aJ\u001a\u00106\u001a\b\u0012\u0004\u0012\u00028\u00000\u00002\f\u00105\u001a\b\u0012\u0004\u0012\u00028\u00000.J\u001c\u00107\u001a\u0002082\u0014\u00109\u001a\u0010\u0012\f\u0012\n\u0012\u0006\b\u0000\u0012\u00028\u00000\b0\u0007J\u0006\u0010:\u001a\u00020\tJ/\u0010:\u001a\u00020\t2\"\u00109\u001a\u0012\u0012\u000e\b\u0001\u0012\n\u0012\u0006\b\u0000\u0012\u00028\u00000\b0;\"\n\u0012\u0006\b\u0000\u0012\u00028\u00000\b¢\u0006\u0002\u0010<J#\u0010=\u001a\u00020\t2\u0014\u00109\u001a\u0010\u0012\f\u0012\n\u0012\u0006\b\u0000\u0012\u00028\u00000\b0\u0007H\u0000¢\u0006\u0002\b>J\b\u0010?\u001a\u00020\tH\u0002J(\u0010@\u001a\u0006\u0012\u0002\b\u00030A2\n\u0010B\u001a\u0006\u0012\u0002\b\u00030A2\u000e\u0010C\u001a\n\u0012\u0006\b\u0000\u0012\u00028\u00000\bH\u0002J.\u0010D\u001a\b\u0012\u0004\u0012\u00028\u00000\u00002\u000e\u0010C\u001a\n\u0012\u0006\b\u0000\u0012\u00028\u00000\b2\u0006\u0010E\u001a\u00020F2\b\b\u0002\u0010G\u001a\u00020\u0011JB\u0010D\u001a\b\u0012\u0004\u0012\u00028\u00000\u00002\u000e\u0010C\u001a\n\u0012\u0006\b\u0000\u0012\u00028\u00000\b2\u0006\u0010E\u001a\u00020F2\b\b\u0002\u0010H\u001a\u00020F2\b\b\u0002\u0010I\u001a\u00020F2\b\b\u0002\u0010J\u001a\u00020FJ@\u0010K\u001a\b\u0012\u0004\u0012\u00028\u00000\u00002\u000e\u0010C\u001a\n\u0012\u0006\b\u0000\u0012\u00028\u00000\b2\u0006\u0010E\u001a\u00020F2\u0006\u0010L\u001a\u00020\u00112\u0006\u0010M\u001a\u00020\u00132\b\b\u0002\u0010N\u001a\u000208H\u0007J\u001b\u0010O\u001a\u0010\u0012\f\u0012\n\u0012\u0006\b\u0000\u0012\u00028\u00000\b0\u0007H\u0000¢\u0006\u0002\bPJ%\u0010Q\u001a\u00020\u001d2\u000e\u0010C\u001a\n\u0012\u0006\b\u0000\u0012\u00028\u00000\b2\u0006\u0010\u0003\u001a\u00028\u0000H\u0002¢\u0006\u0002\u0010RJ%\u0010S\u001a\u00020&2\u000e\u0010C\u001a\n\u0012\u0006\b\u0000\u0012\u00028\u00000\b2\u0006\u0010\u0003\u001a\u00028\u0000H\u0002¢\u0006\u0002\u0010TJ\u0016\u0010U\u001a\u0002082\u000e\u0010C\u001a\n\u0012\u0006\b\u0000\u0012\u00028\u00000\bJ\u0006\u0010V\u001a\u000208J\u0010\u0010W\u001a\u0002082\u0006\u0010X\u001a\u00020FH\u0002J\u000e\u0010Y\u001a\u00020\t2\u0006\u0010Z\u001a\u00020\u000fJ\u000e\u0010[\u001a\u00020\t2\u0006\u0010\u0010\u001a\u00020\u0011J\u000e\u0010\\\u001a\u00020\t2\u0006\u0010\u0012\u001a\u00020\u0013J$\u0010]\u001a\b\u0012\u0004\u0012\u00028\u00000\u00002\u000e\u0010C\u001a\n\u0012\u0006\b\u0000\u0012\u00028\u00000\b2\u0006\u0010^\u001a\u00020FJ.\u0010]\u001a\b\u0012\u0004\u0012\u00028\u00000\u00002\u000e\u0010C\u001a\n\u0012\u0006\b\u0000\u0012\u00028\u00000\b2\u0006\u0010^\u001a\u00020F2\b\b\u0002\u0010G\u001a\u00020\u0013J6\u0010]\u001a\b\u0012\u0004\u0012\u00028\u00000\u00002\u000e\u0010C\u001a\n\u0012\u0006\b\u0000\u0012\u00028\u00000\b2\u0006\u0010^\u001a\u00020F2\u0006\u0010E\u001a\u00020F2\b\b\u0002\u0010G\u001a\u00020\u0013JB\u0010]\u001a\b\u0012\u0004\u0012\u00028\u00000\u00002\u000e\u0010C\u001a\n\u0012\u0006\b\u0000\u0012\u00028\u00000\b2\u0006\u0010^\u001a\u00020F2\b\b\u0002\u0010E\u001a\u00020F2\b\b\u0002\u0010_\u001a\u00020F2\b\b\u0002\u0010`\u001a\u00020FJ\u0006\u0010a\u001a\u00020\tJ\r\u0010b\u001a\u00020\tH\u0000¢\u0006\u0002\bcJA\u0010d\u001a\b\u0012\u0004\u0012\u00028\u00000\u00002.\u0010\u0014\u001a\u0018\u0012\u0014\b\u0001\u0012\u0010\u0012\u0004\u0012\u00020\t\u0018\u00010\u0016j\u0004\u0018\u0001`\u00170;\"\u0010\u0012\u0004\u0012\u00020\t\u0018\u00010\u0016j\u0004\u0018\u0001`\u0017¢\u0006\u0002\u0010eJ)\u0010d\u001a\b\u0012\u0004\u0012\u00028\u00000\u00002\u0016\u0010\u0014\u001a\f\u0012\b\b\u0001\u0012\u0004\u0018\u00010f0;\"\u0004\u0018\u00010f¢\u0006\u0002\u0010gR4\u0010\u0005\u001a\u001c\u0012\u0012\u0012\u0010\u0012\f\u0012\n\u0012\u0006\b\u0000\u0012\u00028\u00000\b0\u0007\u0012\u0004\u0012\u00020\t0\u0006X\u0080\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\n\u0010\u000b\"\u0004\b\f\u0010\rR\u0010\u0010\u000e\u001a\u0004\u0018\u00010\u000fX\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082\u000e¢\u0006\u0002\n\u0000R2\u0010\u0014\u001a&\u0012\u000e\u0012\f\u0012\u0004\u0012\u00020\t0\u0016j\u0002`\u00170\u0015j\u0012\u0012\u000e\u0012\f\u0012\u0004\u0012\u00020\t0\u0016j\u0002`\u0017`\u0018X\u0082\u0004¢\u0006\u0002\n\u0000R*\u0010\u0019\u001a\u001e\u0012\n\u0012\b\u0012\u0004\u0012\u00028\u00000\u001a0\u0015j\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00028\u00000\u001a`\u0018X\u0082\u0004¢\u0006\u0002\n\u0000R\"\u0010\u001b\u001a\u0016\u0012\f\u0012\n\u0012\u0006\b\u0000\u0012\u00028\u00000\b\u0012\u0004\u0012\u00020\u001d0\u001cX\u0082\u0004¢\u0006\u0002\n\u0000R\"\u0010\u001e\u001a\u0016\u0012\f\u0012\n\u0012\u0006\b\u0000\u0012\u00028\u00000\b\u0012\u0004\u0012\u00020\u00110\u001cX\u0082\u0004¢\u0006\u0002\n\u0000R>\u0010\u001f\u001a&\u0012\u000e\u0012\f0 R\b\u0012\u0004\u0012\u00028\u00000\u00000\u0015j\u0012\u0012\u000e\u0012\f0 R\b\u0012\u0004\u0012\u00028\u00000\u0000`\u0018X\u0080\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b!\u0010\"\"\u0004\b#\u0010$R\"\u0010%\u001a\u0016\u0012\f\u0012\n\u0012\u0006\b\u0000\u0012\u00028\u00000\b\u0012\u0004\u0012\u00020&0\u001cX\u0082\u0004¢\u0006\u0002\n\u0000R\"\u0010'\u001a\u0016\u0012\f\u0012\n\u0012\u0006\b\u0000\u0012\u00028\u00000\b\u0012\u0004\u0012\u00020\u00130\u001cX\u0082\u0004¢\u0006\u0002\n\u0000R \u0010(\u001a\b\u0012\u0004\u0012\u00020\t0\u0016X\u0080\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b)\u0010*\"\u0004\b+\u0010,R*\u0010-\u001a\u001e\u0012\n\u0012\b\u0012\u0004\u0012\u00028\u00000.0\u0015j\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00028\u00000.`\u0018X\u0082\u0004¢\u0006\u0002\n\u0000R\u001f\u0010/\u001a\u0010\u0012\f\u0012\n 1*\u0004\u0018\u00018\u00008\u000000¢\u0006\b\n\u0000\u001a\u0004\b2\u00103¨\u0006o"}, d2 = {"Lcom/android/systemui/util/animation/PhysicsAnimator;", ExifInterface.GPS_DIRECTION_TRUE, "", "target", "(Ljava/lang/Object;)V", "cancelAction", "Lkotlin/Function1;", "", "Landroidx/dynamicanimation/animation/FloatPropertyCompat;", "", "getCancelAction$app_release", "()Lkotlin/jvm/functions/Function1;", "setCancelAction$app_release", "(Lkotlin/jvm/functions/Function1;)V", "customAnimationHandler", "Landroidx/dynamicanimation/animation/AnimationHandler;", "defaultFling", "Lcom/android/systemui/util/animation/PhysicsAnimator$FlingConfig;", "defaultSpring", "Lcom/android/systemui/util/animation/PhysicsAnimator$SpringConfig;", "endActions", "Ljava/util/ArrayList;", "Lkotlin/Function0;", "Lcom/android/systemui/util/animation/EndAction;", "Lkotlin/collections/ArrayList;", "endListeners", "Lcom/android/systemui/util/animation/PhysicsAnimator$EndListener;", "flingAnimations", "Landroid/util/ArrayMap;", "Landroidx/dynamicanimation/animation/FlingAnimation;", "flingConfigs", "internalListeners", "Lcom/android/systemui/util/animation/PhysicsAnimator$InternalListener;", "getInternalListeners$app_release", "()Ljava/util/ArrayList;", "setInternalListeners$app_release", "(Ljava/util/ArrayList;)V", "springAnimations", "Landroidx/dynamicanimation/animation/SpringAnimation;", "springConfigs", "startAction", "getStartAction$app_release", "()Lkotlin/jvm/functions/Function0;", "setStartAction$app_release", "(Lkotlin/jvm/functions/Function0;)V", "updateListeners", "Lcom/android/systemui/util/animation/PhysicsAnimator$UpdateListener;", "weakTarget", "Ljava/lang/ref/WeakReference;", "kotlin.jvm.PlatformType", "getWeakTarget", "()Ljava/lang/ref/WeakReference;", "addEndListener", "listener", "addUpdateListener", "arePropertiesAnimating", "", "properties", "cancel", "", "([Landroidx/dynamicanimation/animation/FloatPropertyCompat;)V", "cancelInternal", "cancelInternal$app_release", "clearAnimator", "configureDynamicAnimation", "Landroidx/dynamicanimation/animation/DynamicAnimation;", "anim", "property", "fling", "startVelocity", "", CtNetAuthor.KEY_CONFIG, "friction", "min", HMIDataConstant.DIRECT_MAX, "flingThenSpring", "flingConfig", "springConfig", "flingMustReachMinOrMax", "getAnimatedProperties", "getAnimatedProperties$app_release", "getFlingAnimation", "(Landroidx/dynamicanimation/animation/FloatPropertyCompat;Ljava/lang/Object;)Landroidx/dynamicanimation/animation/FlingAnimation;", "getSpringAnimation", "(Landroidx/dynamicanimation/animation/FloatPropertyCompat;Ljava/lang/Object;)Landroidx/dynamicanimation/animation/SpringAnimation;", "isPropertyAnimating", "isRunning", "isValidValue", "value", "setCustomAnimationHandler", "handler", "setDefaultFlingConfig", "setDefaultSpringConfig", "spring", "toPosition", "stiffness", "dampingRatio", "start", "startInternal", "startInternal$app_release", "withEndActions", "([Lkotlin/jvm/functions/Function0;)Lcom/android/systemui/util/animation/PhysicsAnimator;", "Ljava/lang/Runnable;", "([Ljava/lang/Runnable;)Lcom/android/systemui/util/animation/PhysicsAnimator;", "AnimationUpdate", "Companion", "EndListener", "FlingConfig", "InternalListener", "SpringConfig", "UpdateListener", "app_release"}, k = 1, mv = {1, 5, 1}, xi = 48)
/* loaded from: classes2.dex */
public final class PhysicsAnimator<T> {

    /* renamed from: Companion, reason: from kotlin metadata */
    public static final Companion INSTANCE = new Companion(null);
    private static Function1<Object, ? extends PhysicsAnimator<?>> instanceConstructor = PhysicsAnimator$Companion$instanceConstructor$1.INSTANCE;
    private Function1<? super Set<? extends FloatPropertyCompat<? super T>>, Unit> cancelAction;
    private AnimationHandler customAnimationHandler;
    private FlingConfig defaultFling;
    private SpringConfig defaultSpring;
    private final ArrayList<Function0<Unit>> endActions;
    private final ArrayList<EndListener<T>> endListeners;
    private final ArrayMap<FloatPropertyCompat<? super T>, FlingAnimation> flingAnimations;
    private final ArrayMap<FloatPropertyCompat<? super T>, FlingConfig> flingConfigs;
    private ArrayList<PhysicsAnimator<T>.InternalListener> internalListeners;
    private final ArrayMap<FloatPropertyCompat<? super T>, SpringAnimation> springAnimations;
    private final ArrayMap<FloatPropertyCompat<? super T>, SpringConfig> springConfigs;
    private Function0<Unit> startAction;
    private final ArrayList<UpdateListener<T>> updateListeners;
    private final WeakReference<T> weakTarget;

    /* compiled from: PhysicsAnimator.kt */
    @Metadata(d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0004\bf\u0018\u0000*\u0004\b\u0001\u0010\u00012\u00020\u0002JM\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00028\u00012\u000e\u0010\u0006\u001a\n\u0012\u0006\b\u0000\u0012\u00028\u00010\u00072\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\t2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\f2\u0006\u0010\u000e\u001a\u00020\tH&¢\u0006\u0002\u0010\u000f¨\u0006\u0010"}, d2 = {"Lcom/android/systemui/util/animation/PhysicsAnimator$EndListener;", ExifInterface.GPS_DIRECTION_TRUE, "", "onAnimationEnd", "", "target", "property", "Landroidx/dynamicanimation/animation/FloatPropertyCompat;", "wasFling", "", "canceled", "finalValue", "", "finalVelocity", "allRelevantPropertyAnimsEnded", "(Ljava/lang/Object;Landroidx/dynamicanimation/animation/FloatPropertyCompat;ZZFFZ)V", "app_release"}, k = 1, mv = {1, 5, 1}, xi = 48)
    public interface EndListener<T> {
        void onAnimationEnd(T target, FloatPropertyCompat<? super T> property, boolean wasFling, boolean canceled, float finalValue, float finalVelocity, boolean allRelevantPropertyAnimsEnded);
    }

    /* compiled from: PhysicsAnimator.kt */
    @Metadata(d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\bf\u0018\u0000*\u0004\b\u0001\u0010\u00012\u00020\u0002J;\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00028\u00012$\u0010\u0006\u001a \u0012\f\u0012\n\u0012\u0006\b\u0000\u0012\u00028\u00010\b\u0012\u0004\u0012\u00020\t0\u0007j\b\u0012\u0004\u0012\u00028\u0001`\nH&¢\u0006\u0002\u0010\u000b¨\u0006\f"}, d2 = {"Lcom/android/systemui/util/animation/PhysicsAnimator$UpdateListener;", ExifInterface.GPS_DIRECTION_TRUE, "", "onAnimationUpdateForProperty", "", "target", "values", "Landroid/util/ArrayMap;", "Landroidx/dynamicanimation/animation/FloatPropertyCompat;", "Lcom/android/systemui/util/animation/PhysicsAnimator$AnimationUpdate;", "Lcom/android/systemui/util/animation/UpdateMap;", "(Ljava/lang/Object;Landroid/util/ArrayMap;)V", "app_release"}, k = 1, mv = {1, 5, 1}, xi = 48)
    public interface UpdateListener<T> {
        void onAnimationUpdateForProperty(T target, ArrayMap<FloatPropertyCompat<? super T>, AnimationUpdate> values);
    }

    public /* synthetic */ PhysicsAnimator(Object obj, DefaultConstructorMarker defaultConstructorMarker) {
        this(obj);
    }

    @JvmStatic
    public static final float estimateFlingEndValue(float f, float f2, FlingConfig flingConfig) {
        return INSTANCE.estimateFlingEndValue(f, f2, flingConfig);
    }

    @JvmStatic
    public static final <T> PhysicsAnimator<T> getInstance(T t) {
        return INSTANCE.getInstance(t);
    }

    @JvmStatic
    public static final String getReadablePropertyName(FloatPropertyCompat<?> floatPropertyCompat) {
        return INSTANCE.getReadablePropertyName(floatPropertyCompat);
    }

    private final boolean isValidValue(float value) {
        return value < Float.MAX_VALUE && value > -3.4028235E38f;
    }

    @JvmStatic
    public static final void setVerboseLogging(boolean z) {
        INSTANCE.setVerboseLogging(z);
    }

    public final PhysicsAnimator<T> flingThenSpring(FloatPropertyCompat<? super T> property, float f, FlingConfig flingConfig, SpringConfig springConfig) {
        Intrinsics.checkNotNullParameter(property, "property");
        Intrinsics.checkNotNullParameter(flingConfig, "flingConfig");
        Intrinsics.checkNotNullParameter(springConfig, "springConfig");
        return flingThenSpring$default(this, property, f, flingConfig, springConfig, false, 16, null);
    }

    private PhysicsAnimator(T t) {
        SpringConfig springConfig;
        FlingConfig flingConfig;
        this.weakTarget = new WeakReference<>(t);
        this.springAnimations = new ArrayMap<>();
        this.flingAnimations = new ArrayMap<>();
        this.springConfigs = new ArrayMap<>();
        this.flingConfigs = new ArrayMap<>();
        this.updateListeners = new ArrayList<>();
        this.endListeners = new ArrayList<>();
        this.endActions = new ArrayList<>();
        springConfig = PhysicsAnimatorKt.globalDefaultSpring;
        this.defaultSpring = springConfig;
        flingConfig = PhysicsAnimatorKt.globalDefaultFling;
        this.defaultFling = flingConfig;
        this.internalListeners = new ArrayList<>();
        this.startAction = new PhysicsAnimator$startAction$1(this);
        this.cancelAction = new PhysicsAnimator$cancelAction$1(this);
    }

    public final WeakReference<T> getWeakTarget() {
        return this.weakTarget;
    }

    /* compiled from: PhysicsAnimator.kt */
    @Metadata(d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0007\n\u0002\b\t\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003¢\u0006\u0002\u0010\u0005J\t\u0010\t\u001a\u00020\u0003HÆ\u0003J\t\u0010\n\u001a\u00020\u0003HÆ\u0003J\u001d\u0010\u000b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0003HÆ\u0001J\u0013\u0010\f\u001a\u00020\r2\b\u0010\u000e\u001a\u0004\u0018\u00010\u0001HÖ\u0003J\t\u0010\u000f\u001a\u00020\u0010HÖ\u0001J\t\u0010\u0011\u001a\u00020\u0012HÖ\u0001R\u0011\u0010\u0002\u001a\u00020\u0003¢\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0011\u0010\u0004\u001a\u00020\u0003¢\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0007¨\u0006\u0013"}, d2 = {"Lcom/android/systemui/util/animation/PhysicsAnimator$AnimationUpdate;", "", "value", "", "velocity", "(FF)V", "getValue", "()F", "getVelocity", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_release"}, k = 1, mv = {1, 5, 1}, xi = 48)
    public static final /* data */ class AnimationUpdate {
        private final float value;
        private final float velocity;

        public static /* synthetic */ AnimationUpdate copy$default(AnimationUpdate animationUpdate, float f, float f2, int i, Object obj) {
            if ((i & 1) != 0) {
                f = animationUpdate.value;
            }
            if ((i & 2) != 0) {
                f2 = animationUpdate.velocity;
            }
            return animationUpdate.copy(f, f2);
        }

        /* renamed from: component1, reason: from getter */
        public final float getValue() {
            return this.value;
        }

        /* renamed from: component2, reason: from getter */
        public final float getVelocity() {
            return this.velocity;
        }

        public final AnimationUpdate copy(float value, float velocity) {
            return new AnimationUpdate(value, velocity);
        }

        public boolean equals(Object other) {
            if (this == other) {
                return true;
            }
            if (!(other instanceof AnimationUpdate)) {
                return false;
            }
            AnimationUpdate animationUpdate = (AnimationUpdate) other;
            return Intrinsics.areEqual((Object) Float.valueOf(this.value), (Object) Float.valueOf(animationUpdate.value)) && Intrinsics.areEqual((Object) Float.valueOf(this.velocity), (Object) Float.valueOf(animationUpdate.velocity));
        }

        public int hashCode() {
            return (Float.hashCode(this.value) * 31) + Float.hashCode(this.velocity);
        }

        public String toString() {
            return "AnimationUpdate(value=" + this.value + ", velocity=" + this.velocity + ')';
        }

        public AnimationUpdate(float f, float f2) {
            this.value = f;
            this.velocity = f2;
        }

        public final float getValue() {
            return this.value;
        }

        public final float getVelocity() {
            return this.velocity;
        }
    }

    public final ArrayList<PhysicsAnimator<T>.InternalListener> getInternalListeners$app_release() {
        return this.internalListeners;
    }

    public final void setInternalListeners$app_release(ArrayList<PhysicsAnimator<T>.InternalListener> arrayList) {
        Intrinsics.checkNotNullParameter(arrayList, "<set-?>");
        this.internalListeners = arrayList;
    }

    public final Function0<Unit> getStartAction$app_release() {
        return this.startAction;
    }

    public final void setStartAction$app_release(Function0<Unit> function0) {
        Intrinsics.checkNotNullParameter(function0, "<set-?>");
        this.startAction = function0;
    }

    public final Function1<Set<? extends FloatPropertyCompat<? super T>>, Unit> getCancelAction$app_release() {
        return this.cancelAction;
    }

    public final void setCancelAction$app_release(Function1<? super Set<? extends FloatPropertyCompat<? super T>>, Unit> function1) {
        Intrinsics.checkNotNullParameter(function1, "<set-?>");
        this.cancelAction = function1;
    }

    public static /* synthetic */ PhysicsAnimator spring$default(PhysicsAnimator physicsAnimator, FloatPropertyCompat floatPropertyCompat, float f, float f2, float f3, float f4, int i, Object obj) {
        if ((i & 4) != 0) {
            f2 = 0.0f;
        }
        float f5 = f2;
        if ((i & 8) != 0) {
            f3 = physicsAnimator.defaultSpring.getStiffness$app_release();
        }
        float f6 = f3;
        if ((i & 16) != 0) {
            f4 = physicsAnimator.defaultSpring.getDampingRatio$app_release();
        }
        return physicsAnimator.spring(floatPropertyCompat, f, f5, f6, f4);
    }

    public final PhysicsAnimator<T> spring(FloatPropertyCompat<? super T> property, float toPosition, float startVelocity, float stiffness, float dampingRatio) {
        boolean z;
        Intrinsics.checkNotNullParameter(property, "property");
        z = PhysicsAnimatorKt.verboseLogging;
        if (z) {
            Log.d("PhysicsAnimator", "Springing " + INSTANCE.getReadablePropertyName(property) + " to " + toPosition + '.');
        }
        this.springConfigs.put(property, new SpringConfig(stiffness, dampingRatio, startVelocity, toPosition));
        return this;
    }

    public static /* synthetic */ PhysicsAnimator spring$default(PhysicsAnimator physicsAnimator, FloatPropertyCompat floatPropertyCompat, float f, float f2, SpringConfig springConfig, int i, Object obj) {
        if ((i & 8) != 0) {
            springConfig = physicsAnimator.defaultSpring;
        }
        return physicsAnimator.spring(floatPropertyCompat, f, f2, springConfig);
    }

    public final PhysicsAnimator<T> spring(FloatPropertyCompat<? super T> property, float toPosition, float startVelocity, SpringConfig config) {
        Intrinsics.checkNotNullParameter(property, "property");
        Intrinsics.checkNotNullParameter(config, "config");
        return spring(property, toPosition, startVelocity, config.getStiffness$app_release(), config.getDampingRatio$app_release());
    }

    public static /* synthetic */ PhysicsAnimator spring$default(PhysicsAnimator physicsAnimator, FloatPropertyCompat floatPropertyCompat, float f, SpringConfig springConfig, int i, Object obj) {
        if ((i & 4) != 0) {
            springConfig = physicsAnimator.defaultSpring;
        }
        return physicsAnimator.spring(floatPropertyCompat, f, springConfig);
    }

    public final PhysicsAnimator<T> spring(FloatPropertyCompat<? super T> property, float toPosition, SpringConfig config) {
        Intrinsics.checkNotNullParameter(property, "property");
        Intrinsics.checkNotNullParameter(config, "config");
        return spring(property, toPosition, 0.0f, config);
    }

    public final PhysicsAnimator<T> spring(FloatPropertyCompat<? super T> property, float toPosition) {
        Intrinsics.checkNotNullParameter(property, "property");
        return spring$default(this, property, toPosition, 0.0f, null, 8, null);
    }

    public static /* synthetic */ PhysicsAnimator fling$default(PhysicsAnimator physicsAnimator, FloatPropertyCompat floatPropertyCompat, float f, float f2, float f3, float f4, int i, Object obj) {
        if ((i & 4) != 0) {
            f2 = physicsAnimator.defaultFling.getFriction$app_release();
        }
        float f5 = f2;
        if ((i & 8) != 0) {
            f3 = physicsAnimator.defaultFling.getMin$app_release();
        }
        float f6 = f3;
        if ((i & 16) != 0) {
            f4 = physicsAnimator.defaultFling.getMax$app_release();
        }
        return physicsAnimator.fling(floatPropertyCompat, f, f5, f6, f4);
    }

    public final PhysicsAnimator<T> fling(FloatPropertyCompat<? super T> property, float startVelocity, float friction, float min, float max) {
        boolean z;
        Intrinsics.checkNotNullParameter(property, "property");
        z = PhysicsAnimatorKt.verboseLogging;
        if (z) {
            Log.d("PhysicsAnimator", "Flinging " + INSTANCE.getReadablePropertyName(property) + " with velocity " + startVelocity + '.');
        }
        this.flingConfigs.put(property, new FlingConfig(friction, min, max, startVelocity));
        return this;
    }

    public static /* synthetic */ PhysicsAnimator fling$default(PhysicsAnimator physicsAnimator, FloatPropertyCompat floatPropertyCompat, float f, FlingConfig flingConfig, int i, Object obj) {
        if ((i & 4) != 0) {
            flingConfig = physicsAnimator.defaultFling;
        }
        return physicsAnimator.fling(floatPropertyCompat, f, flingConfig);
    }

    public final PhysicsAnimator<T> fling(FloatPropertyCompat<? super T> property, float startVelocity, FlingConfig config) {
        Intrinsics.checkNotNullParameter(property, "property");
        Intrinsics.checkNotNullParameter(config, "config");
        return fling(property, startVelocity, config.getFriction$app_release(), config.getMin$app_release(), config.getMax$app_release());
    }

    public static /* synthetic */ PhysicsAnimator flingThenSpring$default(PhysicsAnimator physicsAnimator, FloatPropertyCompat floatPropertyCompat, float f, FlingConfig flingConfig, SpringConfig springConfig, boolean z, int i, Object obj) {
        if ((i & 16) != 0) {
            z = false;
        }
        return physicsAnimator.flingThenSpring(floatPropertyCompat, f, flingConfig, springConfig, z);
    }

    public final PhysicsAnimator<T> flingThenSpring(FloatPropertyCompat<? super T> property, float startVelocity, FlingConfig flingConfig, SpringConfig springConfig, boolean flingMustReachMinOrMax) {
        Intrinsics.checkNotNullParameter(property, "property");
        Intrinsics.checkNotNullParameter(flingConfig, "flingConfig");
        Intrinsics.checkNotNullParameter(springConfig, "springConfig");
        T t = this.weakTarget.get();
        if (t == null) {
            Log.w("PhysicsAnimator", "Trying to animate a GC-ed target.");
            return this;
        }
        FlingConfig copy$default = FlingConfig.copy$default(flingConfig, 0.0f, 0.0f, 0.0f, 0.0f, 15, null);
        SpringConfig copy$default2 = SpringConfig.copy$default(springConfig, 0.0f, 0.0f, 0.0f, 0.0f, 15, null);
        float min$app_release = startVelocity < 0.0f ? flingConfig.getMin$app_release() : flingConfig.getMax$app_release();
        if (flingMustReachMinOrMax && isValidValue(min$app_release)) {
            float value = property.getValue(t) + (startVelocity / (flingConfig.getFriction$app_release() * 4.2f));
            float min$app_release2 = (flingConfig.getMin$app_release() + flingConfig.getMax$app_release()) / 2;
            if ((startVelocity < 0.0f && value > min$app_release2) || (startVelocity > 0.0f && value < min$app_release2)) {
                float min$app_release3 = value < min$app_release2 ? flingConfig.getMin$app_release() : flingConfig.getMax$app_release();
                if (isValidValue(min$app_release3)) {
                    return spring(property, min$app_release3, startVelocity, springConfig);
                }
            }
            float value2 = min$app_release - property.getValue(t);
            float friction$app_release = flingConfig.getFriction$app_release() * 4.2f * value2;
            if (value2 > 0.0f && startVelocity >= 0.0f) {
                startVelocity = Math.max(friction$app_release, startVelocity);
            } else if (value2 < 0.0f && startVelocity <= 0.0f) {
                startVelocity = Math.min(friction$app_release, startVelocity);
            }
            copy$default.setStartVelocity$app_release(startVelocity);
            copy$default2.setFinalPosition$app_release(min$app_release);
        } else {
            copy$default.setStartVelocity$app_release(startVelocity);
        }
        this.flingConfigs.put(property, copy$default);
        this.springConfigs.put(property, copy$default2);
        return this;
    }

    public final PhysicsAnimator<T> addUpdateListener(UpdateListener<T> listener) {
        Intrinsics.checkNotNullParameter(listener, "listener");
        this.updateListeners.add(listener);
        return this;
    }

    public final PhysicsAnimator<T> addEndListener(EndListener<T> listener) {
        Intrinsics.checkNotNullParameter(listener, "listener");
        this.endListeners.add(listener);
        return this;
    }

    public final PhysicsAnimator<T> withEndActions(Function0<Unit>... endActions) {
        Intrinsics.checkNotNullParameter(endActions, "endActions");
        this.endActions.addAll(ArraysKt.filterNotNull(endActions));
        return this;
    }

    public final PhysicsAnimator<T> withEndActions(Runnable... endActions) {
        Intrinsics.checkNotNullParameter(endActions, "endActions");
        ArrayList<Function0<Unit>> arrayList = this.endActions;
        List filterNotNull = ArraysKt.filterNotNull(endActions);
        ArrayList arrayList2 = new ArrayList(CollectionsKt.collectionSizeOrDefault(filterNotNull, 10));
        Iterator<E> iterator2 = filterNotNull.iterator2();
        while (iterator2.getHasNext()) {
            arrayList2.add(new PhysicsAnimator$withEndActions$1$1((Runnable) iterator2.next()));
        }
        arrayList.addAll(arrayList2);
        return this;
    }

    public final void setDefaultSpringConfig(SpringConfig defaultSpring) {
        Intrinsics.checkNotNullParameter(defaultSpring, "defaultSpring");
        this.defaultSpring = defaultSpring;
    }

    public final void setDefaultFlingConfig(FlingConfig defaultFling) {
        Intrinsics.checkNotNullParameter(defaultFling, "defaultFling");
        this.defaultFling = defaultFling;
    }

    public final void setCustomAnimationHandler(AnimationHandler handler) {
        Intrinsics.checkNotNullParameter(handler, "handler");
        this.customAnimationHandler = handler;
    }

    public final void start() {
        this.startAction.invoke();
    }

    public final void startInternal$app_release() {
        if (!Looper.getMainLooper().isCurrentThread()) {
            Log.e("PhysicsAnimator", "Animations can only be started on the main thread. If you are seeing this message in a test, call PhysicsAnimatorTestUtils#prepareForTest in your test setup.");
        }
        final T t = this.weakTarget.get();
        if (t == null) {
            Log.w("PhysicsAnimator", "Trying to animate a GC-ed object.");
            return;
        }
        ArrayList arrayList = new ArrayList();
        for (final FloatPropertyCompat<? super T> floatPropertyCompat : getAnimatedProperties$app_release()) {
            final FlingConfig flingConfig = this.flingConfigs.get(floatPropertyCompat);
            final SpringConfig springConfig = this.springConfigs.get(floatPropertyCompat);
            final float value = floatPropertyCompat.getValue(t);
            if (flingConfig != null) {
                arrayList.add(new Function0<Unit>() { // from class: com.android.systemui.util.animation.PhysicsAnimator$startInternal$1
                    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
                    {
                        super(0);
                    }

                    @Override // kotlin.jvm.functions.Function0
                    public /* bridge */ /* synthetic */ Unit invoke() {
                        invoke2();
                        return Unit.INSTANCE;
                    }

                    /* renamed from: invoke, reason: avoid collision after fix types in other method */
                    public final void invoke2() {
                        FlingAnimation flingAnimation;
                        AnimationHandler animationHandler;
                        PhysicsAnimator.FlingConfig flingConfig2 = PhysicsAnimator.FlingConfig.this;
                        float f = value;
                        flingConfig2.setMin$app_release(Math.min(f, flingConfig2.getMin$app_release()));
                        flingConfig2.setMax$app_release(Math.max(f, flingConfig2.getMax$app_release()));
                        this.cancel(floatPropertyCompat);
                        flingAnimation = this.getFlingAnimation(floatPropertyCompat, t);
                        animationHandler = ((PhysicsAnimator) this).customAnimationHandler;
                        if (animationHandler == null) {
                            animationHandler = flingAnimation.getAnimationHandler();
                            Intrinsics.checkNotNullExpressionValue(animationHandler, "flingAnim.animationHandler");
                        }
                        flingAnimation.setAnimationHandler(animationHandler);
                        PhysicsAnimator.FlingConfig.this.applyToAnimation$app_release(flingAnimation);
                        flingAnimation.start();
                    }
                });
            }
            if (springConfig != null) {
                if (flingConfig == null) {
                    SpringAnimation springAnimation = getSpringAnimation(floatPropertyCompat, t);
                    if (this.customAnimationHandler != null && !Intrinsics.areEqual(springAnimation.getAnimationHandler(), this.customAnimationHandler)) {
                        if (springAnimation.isRunning()) {
                            cancel(floatPropertyCompat);
                        }
                        AnimationHandler animationHandler = this.customAnimationHandler;
                        if (animationHandler == null) {
                            animationHandler = springAnimation.getAnimationHandler();
                            Intrinsics.checkNotNullExpressionValue(animationHandler, "springAnim.animationHandler");
                        }
                        springAnimation.setAnimationHandler(animationHandler);
                    }
                    springConfig.applyToAnimation$app_release(springAnimation);
                    arrayList.add(new PhysicsAnimator$startInternal$2(springAnimation));
                } else {
                    final float min$app_release = flingConfig.getMin$app_release();
                    final float max$app_release = flingConfig.getMax$app_release();
                    this.endListeners.add(0, new EndListener<T>() { // from class: com.android.systemui.util.animation.PhysicsAnimator$startInternal$3
                        @Override // com.android.systemui.util.animation.PhysicsAnimator.EndListener
                        public void onAnimationEnd(T target, FloatPropertyCompat<? super T> property, boolean wasFling, boolean canceled, float finalValue, float finalVelocity, boolean allRelevantPropertyAnimsEnded) {
                            float f;
                            SpringAnimation springAnimation2;
                            AnimationHandler animationHandler2;
                            Intrinsics.checkNotNullParameter(property, "property");
                            if (Intrinsics.areEqual(property, floatPropertyCompat) && wasFling && !canceled) {
                                boolean z = Math.abs(finalVelocity) > 0.0f;
                                boolean z2 = !(min$app_release <= finalValue && finalValue <= max$app_release);
                                if (z || z2) {
                                    springConfig.setStartVelocity$app_release(finalVelocity);
                                    float finalPosition$app_release = springConfig.getFinalPosition$app_release();
                                    f = PhysicsAnimatorKt.UNSET;
                                    if (finalPosition$app_release == f) {
                                        if (z) {
                                            springConfig.setFinalPosition$app_release(finalVelocity < 0.0f ? min$app_release : max$app_release);
                                        } else if (z2) {
                                            PhysicsAnimator.SpringConfig springConfig2 = springConfig;
                                            float f2 = min$app_release;
                                            if (finalValue >= f2) {
                                                f2 = max$app_release;
                                            }
                                            springConfig2.setFinalPosition$app_release(f2);
                                        }
                                    }
                                    springAnimation2 = this.getSpringAnimation(floatPropertyCompat, target);
                                    animationHandler2 = ((PhysicsAnimator) this).customAnimationHandler;
                                    if (animationHandler2 == null) {
                                        animationHandler2 = springAnimation2.getAnimationHandler();
                                        Intrinsics.checkNotNullExpressionValue(animationHandler2, "springAnim.animationHandler");
                                    }
                                    springAnimation2.setAnimationHandler(animationHandler2);
                                    springConfig.applyToAnimation$app_release(springAnimation2);
                                    springAnimation2.start();
                                }
                            }
                        }
                    });
                }
            }
        }
        this.internalListeners.add(new InternalListener(this, t, getAnimatedProperties$app_release(), new ArrayList(this.updateListeners), new ArrayList(this.endListeners), new ArrayList(this.endActions)));
        Iterator<E> iterator2 = arrayList.iterator2();
        while (iterator2.getHasNext()) {
            ((Function0) iterator2.next()).invoke();
        }
        clearAnimator();
    }

    private final void clearAnimator() {
        this.springConfigs.clear();
        this.flingConfigs.clear();
        this.updateListeners.clear();
        this.endListeners.clear();
        this.endActions.clear();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final SpringAnimation getSpringAnimation(FloatPropertyCompat<? super T> property, T target) {
        ArrayMap<FloatPropertyCompat<? super T>, SpringAnimation> arrayMap = this.springAnimations;
        SpringAnimation springAnimation = arrayMap.get(property);
        if (springAnimation == null) {
            springAnimation = (SpringAnimation) configureDynamicAnimation(new SpringAnimation(target, (FloatPropertyCompat<T>) property), property);
            arrayMap.put(property, springAnimation);
        }
        Intrinsics.checkNotNullExpressionValue(springAnimation, "springAnimations.getOrPu…    as SpringAnimation })");
        return springAnimation;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final FlingAnimation getFlingAnimation(FloatPropertyCompat<? super T> property, T target) {
        ArrayMap<FloatPropertyCompat<? super T>, FlingAnimation> arrayMap = this.flingAnimations;
        FlingAnimation flingAnimation = arrayMap.get(property);
        if (flingAnimation == null) {
            flingAnimation = (FlingAnimation) configureDynamicAnimation(new FlingAnimation(target, property), property);
            arrayMap.put(property, flingAnimation);
        }
        Intrinsics.checkNotNullExpressionValue(flingAnimation, "flingAnimations.getOrPut…     as FlingAnimation })");
        return flingAnimation;
    }

    private final DynamicAnimation<?> configureDynamicAnimation(final DynamicAnimation<?> anim, final FloatPropertyCompat<? super T> property) {
        anim.addUpdateListener(new DynamicAnimation.OnAnimationUpdateListener() { // from class: com.android.systemui.util.animation.-$$Lambda$PhysicsAnimator$mRsJ8E5ohbx1qoaDXVvLOO8MKIw
            @Override // androidx.dynamicanimation.animation.DynamicAnimation.OnAnimationUpdateListener
            public final void onAnimationUpdate(DynamicAnimation dynamicAnimation, float f, float f2) {
                PhysicsAnimator.m311configureDynamicAnimation$lambda4(PhysicsAnimator.this, property, dynamicAnimation, f, f2);
            }
        });
        anim.addEndListener(new DynamicAnimation.OnAnimationEndListener() { // from class: com.android.systemui.util.animation.-$$Lambda$PhysicsAnimator$aRDOcDepAEjm-vsmazMtPbO-UTY
            @Override // androidx.dynamicanimation.animation.DynamicAnimation.OnAnimationEndListener
            public final void onAnimationEnd(DynamicAnimation dynamicAnimation, boolean z, float f, float f2) {
                PhysicsAnimator.m312configureDynamicAnimation$lambda5(PhysicsAnimator.this, property, anim, dynamicAnimation, z, f, f2);
            }
        });
        return anim;
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: configureDynamicAnimation$lambda-4, reason: not valid java name */
    public static final void m311configureDynamicAnimation$lambda4(PhysicsAnimator this$0, FloatPropertyCompat property, DynamicAnimation dynamicAnimation, float f, float f2) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        Intrinsics.checkNotNullParameter(property, "$property");
        int size = this$0.internalListeners.size();
        for (int i = 0; i < size; i++) {
            this$0.internalListeners.get(i).onInternalAnimationUpdate$app_release(property, f, f2);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: configureDynamicAnimation$lambda-5, reason: not valid java name */
    public static final void m312configureDynamicAnimation$lambda5(PhysicsAnimator this$0, final FloatPropertyCompat property, final DynamicAnimation anim, DynamicAnimation dynamicAnimation, final boolean z, final float f, final float f2) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        Intrinsics.checkNotNullParameter(property, "$property");
        Intrinsics.checkNotNullParameter(anim, "$anim");
        CollectionsKt.removeAll((List) this$0.internalListeners, (Function1) new Function1<PhysicsAnimator<T>.InternalListener, Boolean>() { // from class: com.android.systemui.util.animation.PhysicsAnimator$configureDynamicAnimation$2$1
            /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
            {
                super(1);
            }

            @Override // kotlin.jvm.functions.Function1
            public final Boolean invoke(PhysicsAnimator<T>.InternalListener it) {
                Intrinsics.checkNotNullParameter(it, "it");
                return Boolean.valueOf(it.onInternalAnimationEnd$app_release(property, z, f, f2, anim instanceof FlingAnimation));
            }
        });
        if (Intrinsics.areEqual(this$0.springAnimations.get(property), anim)) {
            this$0.springAnimations.remove(property);
        }
        if (Intrinsics.areEqual(this$0.flingAnimations.get(property), anim)) {
            this$0.flingAnimations.remove(property);
        }
    }

    /* compiled from: PhysicsAnimator.kt */
    @Metadata(d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\"\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b\b\b\u0080\u0004\u0018\u00002\u00020\u0001Bc\u0012\u0006\u0010\u0002\u001a\u00028\u0000\u0012\u0014\u0010\u0003\u001a\u0010\u0012\f\u0012\n\u0012\u0006\b\u0000\u0012\u00028\u00000\u00050\u0004\u0012\u0012\u0010\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00028\u00000\b0\u0007\u0012\u0012\u0010\t\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00028\u00000\n0\u0007\u0012\u0016\u0010\u000b\u001a\u0012\u0012\u000e\u0012\f\u0012\u0004\u0012\u00020\r0\fj\u0002`\u000e0\u0007¢\u0006\u0002\u0010\u000fJ\b\u0010\u0016\u001a\u00020\rH\u0002J=\u0010\u0017\u001a\u00020\u00182\u000e\u0010\u0019\u001a\n\u0012\u0006\b\u0000\u0012\u00028\u00000\u00052\u0006\u0010\u001a\u001a\u00020\u00182\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u001c2\u0006\u0010\u001e\u001a\u00020\u0018H\u0000¢\u0006\u0002\b\u001fJ-\u0010 \u001a\u00020\r2\u000e\u0010\u0019\u001a\n\u0012\u0006\b\u0000\u0012\u00028\u00000\u00052\u0006\u0010!\u001a\u00020\u001c2\u0006\u0010\"\u001a\u00020\u001cH\u0000¢\u0006\u0002\b#R\u001e\u0010\u000b\u001a\u0012\u0012\u000e\u0012\f\u0012\u0004\u0012\u00020\r0\fj\u0002`\u000e0\u0007X\u0082\u000e¢\u0006\u0002\n\u0000R\u001a\u0010\t\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00028\u00000\n0\u0007X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u000e¢\u0006\u0002\n\u0000R\u001c\u0010\u0003\u001a\u0010\u0012\f\u0012\n\u0012\u0006\b\u0000\u0012\u00028\u00000\u00050\u0004X\u0082\u000e¢\u0006\u0002\n\u0000R\u0010\u0010\u0002\u001a\u00028\u0000X\u0082\u0004¢\u0006\u0004\n\u0002\u0010\u0012R\"\u0010\u0013\u001a\u0016\u0012\f\u0012\n\u0012\u0006\b\u0000\u0012\u00028\u00000\u0005\u0012\u0004\u0012\u00020\u00150\u0014X\u0082\u0004¢\u0006\u0002\n\u0000R\u001a\u0010\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00028\u00000\b0\u0007X\u0082\u000e¢\u0006\u0002\n\u0000¨\u0006$"}, d2 = {"Lcom/android/systemui/util/animation/PhysicsAnimator$InternalListener;", "", "target", "properties", "", "Landroidx/dynamicanimation/animation/FloatPropertyCompat;", "updateListeners", "", "Lcom/android/systemui/util/animation/PhysicsAnimator$UpdateListener;", "endListeners", "Lcom/android/systemui/util/animation/PhysicsAnimator$EndListener;", "endActions", "Lkotlin/Function0;", "", "Lcom/android/systemui/util/animation/EndAction;", "(Lcom/android/systemui/util/animation/PhysicsAnimator;Ljava/lang/Object;Ljava/util/Set;Ljava/util/List;Ljava/util/List;Ljava/util/List;)V", "numPropertiesAnimating", "", "Ljava/lang/Object;", "undispatchedUpdates", "Landroid/util/ArrayMap;", "Lcom/android/systemui/util/animation/PhysicsAnimator$AnimationUpdate;", "maybeDispatchUpdates", "onInternalAnimationEnd", "", "property", "canceled", "finalValue", "", "finalVelocity", "isFling", "onInternalAnimationEnd$app_release", "onInternalAnimationUpdate", "value", "velocity", "onInternalAnimationUpdate$app_release", "app_release"}, k = 1, mv = {1, 5, 1}, xi = 48)
    public final class InternalListener {
        private List<? extends Function0<Unit>> endActions;
        private List<? extends EndListener<T>> endListeners;
        private int numPropertiesAnimating;
        private Set<? extends FloatPropertyCompat<? super T>> properties;
        private final T target;
        final /* synthetic */ PhysicsAnimator<T> this$0;
        private final ArrayMap<FloatPropertyCompat<? super T>, AnimationUpdate> undispatchedUpdates;
        private List<? extends UpdateListener<T>> updateListeners;

        public InternalListener(PhysicsAnimator this$0, T t, Set<? extends FloatPropertyCompat<? super T>> properties, List<? extends UpdateListener<T>> updateListeners, List<? extends EndListener<T>> endListeners, List<? extends Function0<Unit>> endActions) {
            Intrinsics.checkNotNullParameter(this$0, "this$0");
            Intrinsics.checkNotNullParameter(properties, "properties");
            Intrinsics.checkNotNullParameter(updateListeners, "updateListeners");
            Intrinsics.checkNotNullParameter(endListeners, "endListeners");
            Intrinsics.checkNotNullParameter(endActions, "endActions");
            this.this$0 = this$0;
            this.target = t;
            this.properties = properties;
            this.updateListeners = updateListeners;
            this.endListeners = endListeners;
            this.endActions = endActions;
            this.numPropertiesAnimating = properties.size();
            this.undispatchedUpdates = new ArrayMap<>();
        }

        public final void onInternalAnimationUpdate$app_release(FloatPropertyCompat<? super T> property, float value, float velocity) {
            Intrinsics.checkNotNullParameter(property, "property");
            if (this.properties.contains(property)) {
                this.undispatchedUpdates.put(property, new AnimationUpdate(value, velocity));
                maybeDispatchUpdates();
            }
        }

        public final boolean onInternalAnimationEnd$app_release(FloatPropertyCompat<? super T> property, boolean canceled, float finalValue, float finalVelocity, boolean isFling) {
            Intrinsics.checkNotNullParameter(property, "property");
            if (!this.properties.contains(property)) {
                return false;
            }
            this.numPropertiesAnimating--;
            maybeDispatchUpdates();
            if (this.undispatchedUpdates.containsKey(property)) {
                for (UpdateListener<T> updateListener : this.updateListeners) {
                    T t = this.target;
                    ArrayMap<FloatPropertyCompat<? super T>, AnimationUpdate> arrayMap = new ArrayMap<>();
                    arrayMap.put(property, this.undispatchedUpdates.get(property));
                    Unit unit = Unit.INSTANCE;
                    updateListener.onAnimationUpdateForProperty(t, arrayMap);
                }
                this.undispatchedUpdates.remove(property);
            }
            boolean z = !this.this$0.arePropertiesAnimating(this.properties);
            List<? extends EndListener<T>> list = this.endListeners;
            PhysicsAnimator<T> physicsAnimator = this.this$0;
            Iterator<? extends EndListener<T>> iterator2 = list.iterator2();
            while (iterator2.getHasNext()) {
                iterator2.next().onAnimationEnd(this.target, property, isFling, canceled, finalValue, finalVelocity, z);
                if (physicsAnimator.isPropertyAnimating(property)) {
                    return false;
                }
            }
            if (z && !canceled) {
                Iterator<? extends Function0<Unit>> iterator22 = this.endActions.iterator2();
                while (iterator22.getHasNext()) {
                    iterator22.next().invoke();
                }
            }
            return z;
        }

        private final void maybeDispatchUpdates() {
            if (this.undispatchedUpdates.size() < this.numPropertiesAnimating || this.undispatchedUpdates.size() <= 0) {
                return;
            }
            Iterator<? extends UpdateListener<T>> iterator2 = this.updateListeners.iterator2();
            while (iterator2.getHasNext()) {
                iterator2.next().onAnimationUpdateForProperty(this.target, new ArrayMap<>(this.undispatchedUpdates));
            }
            this.undispatchedUpdates.clear();
        }
    }

    public final boolean isRunning() {
        Set<FloatPropertyCompat<? super T>> keySet = this.springAnimations.keySet();
        Intrinsics.checkNotNullExpressionValue(keySet, "springAnimations.keys");
        Set<FloatPropertyCompat<? super T>> keySet2 = this.flingAnimations.keySet();
        Intrinsics.checkNotNullExpressionValue(keySet2, "flingAnimations.keys");
        return arePropertiesAnimating(CollectionsKt.union(keySet, keySet2));
    }

    public final boolean isPropertyAnimating(FloatPropertyCompat<? super T> property) {
        Intrinsics.checkNotNullParameter(property, "property");
        SpringAnimation springAnimation = this.springAnimations.get(property);
        if (!(springAnimation == null ? false : springAnimation.isRunning())) {
            FlingAnimation flingAnimation = this.flingAnimations.get(property);
            if (!(flingAnimation == null ? false : flingAnimation.isRunning())) {
                return false;
            }
        }
        return true;
    }

    public final boolean arePropertiesAnimating(Set<? extends FloatPropertyCompat<? super T>> properties) {
        Intrinsics.checkNotNullParameter(properties, "properties");
        Set<? extends FloatPropertyCompat<? super T>> set = properties;
        if ((set instanceof Collection) && set.isEmpty()) {
            return false;
        }
        Iterator<? extends FloatPropertyCompat<? super T>> iterator2 = set.iterator2();
        while (iterator2.getHasNext()) {
            if (isPropertyAnimating(iterator2.next())) {
                return true;
            }
        }
        return false;
    }

    public final Set<FloatPropertyCompat<? super T>> getAnimatedProperties$app_release() {
        Set<FloatPropertyCompat<? super T>> keySet = this.springConfigs.keySet();
        Intrinsics.checkNotNullExpressionValue(keySet, "springConfigs.keys");
        Set<FloatPropertyCompat<? super T>> keySet2 = this.flingConfigs.keySet();
        Intrinsics.checkNotNullExpressionValue(keySet2, "flingConfigs.keys");
        return CollectionsKt.union(keySet, keySet2);
    }

    public final void cancelInternal$app_release(Set<? extends FloatPropertyCompat<? super T>> properties) {
        Intrinsics.checkNotNullParameter(properties, "properties");
        for (FloatPropertyCompat<? super T> floatPropertyCompat : properties) {
            FlingAnimation flingAnimation = this.flingAnimations.get(floatPropertyCompat);
            if (flingAnimation != null) {
                flingAnimation.cancel();
            }
            SpringAnimation springAnimation = this.springAnimations.get(floatPropertyCompat);
            if (springAnimation != null) {
                springAnimation.cancel();
            }
        }
    }

    public final void cancel() {
        Function1<? super Set<? extends FloatPropertyCompat<? super T>>, Unit> function1 = this.cancelAction;
        Set<FloatPropertyCompat<? super T>> keySet = this.flingAnimations.keySet();
        Intrinsics.checkNotNullExpressionValue(keySet, "flingAnimations.keys");
        function1.invoke(keySet);
        Function1<? super Set<? extends FloatPropertyCompat<? super T>>, Unit> function12 = this.cancelAction;
        Set<FloatPropertyCompat<? super T>> keySet2 = this.springAnimations.keySet();
        Intrinsics.checkNotNullExpressionValue(keySet2, "springAnimations.keys");
        function12.invoke(keySet2);
    }

    public final void cancel(FloatPropertyCompat<? super T>... properties) {
        Intrinsics.checkNotNullParameter(properties, "properties");
        this.cancelAction.invoke(ArraysKt.toSet(properties));
    }

    /* compiled from: PhysicsAnimator.kt */
    @Metadata(d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0010\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u0007\b\u0016¢\u0006\u0002\u0010\u0002B\u0017\b\u0016\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\u0006\u0010\u0005\u001a\u00020\u0004¢\u0006\u0002\u0010\u0006B+\b\u0000\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0004\u0012\b\b\u0002\u0010\b\u001a\u00020\u0004¢\u0006\u0002\u0010\tJ\u0015\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u0017H\u0000¢\u0006\u0002\b\u0018J\u000e\u0010\u0019\u001a\u00020\u0004HÀ\u0003¢\u0006\u0002\b\u001aJ\u000e\u0010\u001b\u001a\u00020\u0004HÀ\u0003¢\u0006\u0002\b\u001cJ\u000e\u0010\u001d\u001a\u00020\u0004HÀ\u0003¢\u0006\u0002\b\u001eJ\u000e\u0010\u001f\u001a\u00020\u0004HÀ\u0003¢\u0006\u0002\b J1\u0010!\u001a\u00020\u00002\b\b\u0002\u0010\u0003\u001a\u00020\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u00042\b\b\u0002\u0010\u0007\u001a\u00020\u00042\b\b\u0002\u0010\b\u001a\u00020\u0004HÆ\u0001J\u0013\u0010\"\u001a\u00020#2\b\u0010$\u001a\u0004\u0018\u00010\u0001HÖ\u0003J\t\u0010%\u001a\u00020&HÖ\u0001J\t\u0010'\u001a\u00020(HÖ\u0001R\u001a\u0010\u0005\u001a\u00020\u0004X\u0080\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\n\u0010\u000b\"\u0004\b\f\u0010\rR\u001a\u0010\b\u001a\u00020\u0004X\u0080\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\u000e\u0010\u000b\"\u0004\b\u000f\u0010\rR\u001a\u0010\u0007\u001a\u00020\u0004X\u0080\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\u0010\u0010\u000b\"\u0004\b\u0011\u0010\rR\u001a\u0010\u0003\u001a\u00020\u0004X\u0080\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\u0012\u0010\u000b\"\u0004\b\u0013\u0010\r¨\u0006)"}, d2 = {"Lcom/android/systemui/util/animation/PhysicsAnimator$SpringConfig;", "", "()V", "stiffness", "", "dampingRatio", "(FF)V", "startVelocity", "finalPosition", "(FFFF)V", "getDampingRatio$app_release", "()F", "setDampingRatio$app_release", "(F)V", "getFinalPosition$app_release", "setFinalPosition$app_release", "getStartVelocity$app_release", "setStartVelocity$app_release", "getStiffness$app_release", "setStiffness$app_release", "applyToAnimation", "", "anim", "Landroidx/dynamicanimation/animation/SpringAnimation;", "applyToAnimation$app_release", "component1", "component1$app_release", "component2", "component2$app_release", "component3", "component3$app_release", "component4", "component4$app_release", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_release"}, k = 1, mv = {1, 5, 1}, xi = 48)
    public static final /* data */ class SpringConfig {
        private float dampingRatio;
        private float finalPosition;
        private float startVelocity;
        private float stiffness;

        public static /* synthetic */ SpringConfig copy$default(SpringConfig springConfig, float f, float f2, float f3, float f4, int i, Object obj) {
            if ((i & 1) != 0) {
                f = springConfig.stiffness;
            }
            if ((i & 2) != 0) {
                f2 = springConfig.dampingRatio;
            }
            if ((i & 4) != 0) {
                f3 = springConfig.startVelocity;
            }
            if ((i & 8) != 0) {
                f4 = springConfig.finalPosition;
            }
            return springConfig.copy(f, f2, f3, f4);
        }

        /* renamed from: component1$app_release, reason: from getter */
        public final float getStiffness() {
            return this.stiffness;
        }

        /* renamed from: component2$app_release, reason: from getter */
        public final float getDampingRatio() {
            return this.dampingRatio;
        }

        /* renamed from: component3$app_release, reason: from getter */
        public final float getStartVelocity() {
            return this.startVelocity;
        }

        /* renamed from: component4$app_release, reason: from getter */
        public final float getFinalPosition() {
            return this.finalPosition;
        }

        public final SpringConfig copy(float stiffness, float dampingRatio, float startVelocity, float finalPosition) {
            return new SpringConfig(stiffness, dampingRatio, startVelocity, finalPosition);
        }

        public boolean equals(Object other) {
            if (this == other) {
                return true;
            }
            if (!(other instanceof SpringConfig)) {
                return false;
            }
            SpringConfig springConfig = (SpringConfig) other;
            return Intrinsics.areEqual((Object) Float.valueOf(this.stiffness), (Object) Float.valueOf(springConfig.stiffness)) && Intrinsics.areEqual((Object) Float.valueOf(this.dampingRatio), (Object) Float.valueOf(springConfig.dampingRatio)) && Intrinsics.areEqual((Object) Float.valueOf(this.startVelocity), (Object) Float.valueOf(springConfig.startVelocity)) && Intrinsics.areEqual((Object) Float.valueOf(this.finalPosition), (Object) Float.valueOf(springConfig.finalPosition));
        }

        public int hashCode() {
            return (((((Float.hashCode(this.stiffness) * 31) + Float.hashCode(this.dampingRatio)) * 31) + Float.hashCode(this.startVelocity)) * 31) + Float.hashCode(this.finalPosition);
        }

        public String toString() {
            return "SpringConfig(stiffness=" + this.stiffness + ", dampingRatio=" + this.dampingRatio + ", startVelocity=" + this.startVelocity + ", finalPosition=" + this.finalPosition + ')';
        }

        public SpringConfig(float f, float f2, float f3, float f4) {
            this.stiffness = f;
            this.dampingRatio = f2;
            this.startVelocity = f3;
            this.finalPosition = f4;
        }

        public final float getStiffness$app_release() {
            return this.stiffness;
        }

        public final void setStiffness$app_release(float f) {
            this.stiffness = f;
        }

        public final float getDampingRatio$app_release() {
            return this.dampingRatio;
        }

        public final void setDampingRatio$app_release(float f) {
            this.dampingRatio = f;
        }

        public final float getStartVelocity$app_release() {
            return this.startVelocity;
        }

        public final void setStartVelocity$app_release(float f) {
            this.startVelocity = f;
        }

        public /* synthetic */ SpringConfig(float f, float f2, float f3, float f4, int i, DefaultConstructorMarker defaultConstructorMarker) {
            this(f, f2, (i & 4) != 0 ? 0.0f : f3, (i & 8) != 0 ? PhysicsAnimatorKt.UNSET : f4);
        }

        public final float getFinalPosition$app_release() {
            return this.finalPosition;
        }

        public final void setFinalPosition$app_release(float f) {
            this.finalPosition = f;
        }

        /* JADX WARN: Illegal instructions before constructor call */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public SpringConfig() {
            /*
                r2 = this;
                com.android.systemui.util.animation.PhysicsAnimator$SpringConfig r0 = com.android.systemui.util.animation.PhysicsAnimatorKt.access$getGlobalDefaultSpring$p()
                float r0 = r0.stiffness
                com.android.systemui.util.animation.PhysicsAnimator$SpringConfig r1 = com.android.systemui.util.animation.PhysicsAnimatorKt.access$getGlobalDefaultSpring$p()
                float r1 = r1.dampingRatio
                r2.<init>(r0, r1)
                return
            */
            throw new UnsupportedOperationException("Method not decompiled: com.android.systemui.util.animation.PhysicsAnimator.SpringConfig.<init>():void");
        }

        public SpringConfig(float f, float f2) {
            this(f, f2, 0.0f, 0.0f, 8, null);
        }

        public final void applyToAnimation$app_release(SpringAnimation anim) {
            Intrinsics.checkNotNullParameter(anim, "anim");
            SpringForce spring = anim.getSpring();
            if (spring == null) {
                spring = new SpringForce();
            }
            spring.setStiffness(getStiffness$app_release());
            spring.setDampingRatio(getDampingRatio$app_release());
            spring.setFinalPosition(getFinalPosition$app_release());
            anim.setSpring(spring);
            if (this.startVelocity == 0.0f) {
                return;
            }
            anim.setStartVelocity(this.startVelocity);
        }
    }

    /* compiled from: PhysicsAnimator.kt */
    @Metadata(d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0010\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u0007\b\u0016¢\u0006\u0002\u0010\u0002B\u000f\b\u0016\u0012\u0006\u0010\u0003\u001a\u00020\u0004¢\u0006\u0002\u0010\u0005B\u001f\b\u0016\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\u0006\u0010\u0006\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0004¢\u0006\u0002\u0010\bB'\b\u0000\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\u0006\u0010\u0006\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0004\u0012\u0006\u0010\t\u001a\u00020\u0004¢\u0006\u0002\u0010\nJ\u0015\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u0017H\u0000¢\u0006\u0002\b\u0018J\u000e\u0010\u0019\u001a\u00020\u0004HÀ\u0003¢\u0006\u0002\b\u001aJ\u000e\u0010\u001b\u001a\u00020\u0004HÀ\u0003¢\u0006\u0002\b\u001cJ\u000e\u0010\u001d\u001a\u00020\u0004HÀ\u0003¢\u0006\u0002\b\u001eJ\u000e\u0010\u001f\u001a\u00020\u0004HÀ\u0003¢\u0006\u0002\b J1\u0010!\u001a\u00020\u00002\b\b\u0002\u0010\u0003\u001a\u00020\u00042\b\b\u0002\u0010\u0006\u001a\u00020\u00042\b\b\u0002\u0010\u0007\u001a\u00020\u00042\b\b\u0002\u0010\t\u001a\u00020\u0004HÆ\u0001J\u0013\u0010\"\u001a\u00020#2\b\u0010$\u001a\u0004\u0018\u00010\u0001HÖ\u0003J\t\u0010%\u001a\u00020&HÖ\u0001J\t\u0010'\u001a\u00020(HÖ\u0001R\u001a\u0010\u0003\u001a\u00020\u0004X\u0080\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\u000b\u0010\f\"\u0004\b\r\u0010\u0005R\u001a\u0010\u0007\u001a\u00020\u0004X\u0080\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\u000e\u0010\f\"\u0004\b\u000f\u0010\u0005R\u001a\u0010\u0006\u001a\u00020\u0004X\u0080\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\u0010\u0010\f\"\u0004\b\u0011\u0010\u0005R\u001a\u0010\t\u001a\u00020\u0004X\u0080\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\u0012\u0010\f\"\u0004\b\u0013\u0010\u0005¨\u0006)"}, d2 = {"Lcom/android/systemui/util/animation/PhysicsAnimator$FlingConfig;", "", "()V", "friction", "", "(F)V", "min", HMIDataConstant.DIRECT_MAX, "(FFF)V", "startVelocity", "(FFFF)V", "getFriction$app_release", "()F", "setFriction$app_release", "getMax$app_release", "setMax$app_release", "getMin$app_release", "setMin$app_release", "getStartVelocity$app_release", "setStartVelocity$app_release", "applyToAnimation", "", "anim", "Landroidx/dynamicanimation/animation/FlingAnimation;", "applyToAnimation$app_release", "component1", "component1$app_release", "component2", "component2$app_release", "component3", "component3$app_release", "component4", "component4$app_release", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_release"}, k = 1, mv = {1, 5, 1}, xi = 48)
    public static final /* data */ class FlingConfig {
        private float friction;
        private float max;
        private float min;
        private float startVelocity;

        public static /* synthetic */ FlingConfig copy$default(FlingConfig flingConfig, float f, float f2, float f3, float f4, int i, Object obj) {
            if ((i & 1) != 0) {
                f = flingConfig.friction;
            }
            if ((i & 2) != 0) {
                f2 = flingConfig.min;
            }
            if ((i & 4) != 0) {
                f3 = flingConfig.max;
            }
            if ((i & 8) != 0) {
                f4 = flingConfig.startVelocity;
            }
            return flingConfig.copy(f, f2, f3, f4);
        }

        /* renamed from: component1$app_release, reason: from getter */
        public final float getFriction() {
            return this.friction;
        }

        /* renamed from: component2$app_release, reason: from getter */
        public final float getMin() {
            return this.min;
        }

        /* renamed from: component3$app_release, reason: from getter */
        public final float getMax() {
            return this.max;
        }

        /* renamed from: component4$app_release, reason: from getter */
        public final float getStartVelocity() {
            return this.startVelocity;
        }

        public final FlingConfig copy(float friction, float min, float max, float startVelocity) {
            return new FlingConfig(friction, min, max, startVelocity);
        }

        public boolean equals(Object other) {
            if (this == other) {
                return true;
            }
            if (!(other instanceof FlingConfig)) {
                return false;
            }
            FlingConfig flingConfig = (FlingConfig) other;
            return Intrinsics.areEqual((Object) Float.valueOf(this.friction), (Object) Float.valueOf(flingConfig.friction)) && Intrinsics.areEqual((Object) Float.valueOf(this.min), (Object) Float.valueOf(flingConfig.min)) && Intrinsics.areEqual((Object) Float.valueOf(this.max), (Object) Float.valueOf(flingConfig.max)) && Intrinsics.areEqual((Object) Float.valueOf(this.startVelocity), (Object) Float.valueOf(flingConfig.startVelocity));
        }

        public int hashCode() {
            return (((((Float.hashCode(this.friction) * 31) + Float.hashCode(this.min)) * 31) + Float.hashCode(this.max)) * 31) + Float.hashCode(this.startVelocity);
        }

        public String toString() {
            return "FlingConfig(friction=" + this.friction + ", min=" + this.min + ", max=" + this.max + ", startVelocity=" + this.startVelocity + ')';
        }

        public FlingConfig(float f, float f2, float f3, float f4) {
            this.friction = f;
            this.min = f2;
            this.max = f3;
            this.startVelocity = f4;
        }

        public final float getFriction$app_release() {
            return this.friction;
        }

        public final void setFriction$app_release(float f) {
            this.friction = f;
        }

        public final float getMin$app_release() {
            return this.min;
        }

        public final void setMin$app_release(float f) {
            this.min = f;
        }

        public final float getMax$app_release() {
            return this.max;
        }

        public final void setMax$app_release(float f) {
            this.max = f;
        }

        public final float getStartVelocity$app_release() {
            return this.startVelocity;
        }

        public final void setStartVelocity$app_release(float f) {
            this.startVelocity = f;
        }

        /* JADX WARN: Illegal instructions before constructor call */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public FlingConfig() {
            /*
                r1 = this;
                com.android.systemui.util.animation.PhysicsAnimator$FlingConfig r0 = com.android.systemui.util.animation.PhysicsAnimatorKt.access$getGlobalDefaultFling$p()
                float r0 = r0.friction
                r1.<init>(r0)
                return
            */
            throw new UnsupportedOperationException("Method not decompiled: com.android.systemui.util.animation.PhysicsAnimator.FlingConfig.<init>():void");
        }

        /* JADX WARN: Illegal instructions before constructor call */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public FlingConfig(float r3) {
            /*
                r2 = this;
                com.android.systemui.util.animation.PhysicsAnimator$FlingConfig r0 = com.android.systemui.util.animation.PhysicsAnimatorKt.access$getGlobalDefaultFling$p()
                float r0 = r0.min
                com.android.systemui.util.animation.PhysicsAnimator$FlingConfig r1 = com.android.systemui.util.animation.PhysicsAnimatorKt.access$getGlobalDefaultFling$p()
                float r1 = r1.max
                r2.<init>(r3, r0, r1)
                return
            */
            throw new UnsupportedOperationException("Method not decompiled: com.android.systemui.util.animation.PhysicsAnimator.FlingConfig.<init>(float):void");
        }

        public FlingConfig(float f, float f2, float f3) {
            this(f, f2, f3, 0.0f);
        }

        public final void applyToAnimation$app_release(FlingAnimation anim) {
            Intrinsics.checkNotNullParameter(anim, "anim");
            anim.setFriction(getFriction$app_release());
            anim.setMinValue(getMin$app_release());
            anim.setMaxValue(getMax$app_release());
            anim.setStartVelocity(getStartVelocity$app_release());
        }
    }

    /* compiled from: PhysicsAnimator.kt */
    @Metadata(d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002¢\u0006\u0002\u0010\u0002J \u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\u000b2\u0006\u0010\r\u001a\u00020\u000b2\u0006\u0010\u000e\u001a\u00020\u000fH\u0007J%\u0010\u0010\u001a\b\u0012\u0004\u0012\u0002H\u00110\u0005\"\b\b\u0001\u0010\u0011*\u00020\u00012\u0006\u0010\u0012\u001a\u0002H\u0011H\u0007¢\u0006\u0002\u0010\u0013J\u0014\u0010\u0014\u001a\u00020\u00152\n\u0010\u0016\u001a\u0006\u0012\u0002\b\u00030\u0017H\u0007J\u0010\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001bH\u0007R*\u0010\u0003\u001a\u0012\u0012\u0004\u0012\u00020\u0001\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00050\u0004X\u0080\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\u0006\u0010\u0007\"\u0004\b\b\u0010\t¨\u0006\u001c"}, d2 = {"Lcom/android/systemui/util/animation/PhysicsAnimator$Companion;", "", "()V", "instanceConstructor", "Lkotlin/Function1;", "Lcom/android/systemui/util/animation/PhysicsAnimator;", "getInstanceConstructor$app_release", "()Lkotlin/jvm/functions/Function1;", "setInstanceConstructor$app_release", "(Lkotlin/jvm/functions/Function1;)V", "estimateFlingEndValue", "", "startValue", "startVelocity", "flingConfig", "Lcom/android/systemui/util/animation/PhysicsAnimator$FlingConfig;", "getInstance", ExifInterface.GPS_DIRECTION_TRUE, "target", "(Ljava/lang/Object;)Lcom/android/systemui/util/animation/PhysicsAnimator;", "getReadablePropertyName", "", "property", "Landroidx/dynamicanimation/animation/FloatPropertyCompat;", "setVerboseLogging", "", "debug", "", "app_release"}, k = 1, mv = {1, 5, 1}, xi = 48)
    public static final class Companion {
        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        private Companion() {
        }

        public final Function1<Object, PhysicsAnimator<?>> getInstanceConstructor$app_release() {
            return PhysicsAnimator.instanceConstructor;
        }

        public final void setInstanceConstructor$app_release(Function1<Object, ? extends PhysicsAnimator<?>> function1) {
            Intrinsics.checkNotNullParameter(function1, "<set-?>");
            PhysicsAnimator.instanceConstructor = function1;
        }

        @JvmStatic
        public final <T> PhysicsAnimator<T> getInstance(T target) {
            Intrinsics.checkNotNullParameter(target, "target");
            if (!PhysicsAnimatorKt.getAnimators().containsKey(target)) {
                PhysicsAnimatorKt.getAnimators().put(target, getInstanceConstructor$app_release().invoke(target));
            }
            Object obj = PhysicsAnimatorKt.getAnimators().get(target);
            if (obj != null) {
                return (PhysicsAnimator) obj;
            }
            throw new NullPointerException("null cannot be cast to non-null type com.android.systemui.util.animation.PhysicsAnimator<T of com.android.systemui.util.animation.PhysicsAnimator.Companion.getInstance>");
        }

        @JvmStatic
        public final void setVerboseLogging(boolean debug) {
            PhysicsAnimatorKt.verboseLogging = debug;
        }

        @JvmStatic
        public final float estimateFlingEndValue(float startValue, float startVelocity, FlingConfig flingConfig) {
            Intrinsics.checkNotNullParameter(flingConfig, "flingConfig");
            return Math.min(flingConfig.getMax$app_release(), Math.max(flingConfig.getMin$app_release(), startValue + (startVelocity / (flingConfig.getFriction$app_release() * 4.2f))));
        }

        @JvmStatic
        public final String getReadablePropertyName(FloatPropertyCompat<?> property) {
            Intrinsics.checkNotNullParameter(property, "property");
            return Intrinsics.areEqual(property, DynamicAnimation.TRANSLATION_X) ? "translationX" : Intrinsics.areEqual(property, DynamicAnimation.TRANSLATION_Y) ? "translationY" : Intrinsics.areEqual(property, DynamicAnimation.TRANSLATION_Z) ? "translationZ" : Intrinsics.areEqual(property, DynamicAnimation.SCALE_X) ? "scaleX" : Intrinsics.areEqual(property, DynamicAnimation.SCALE_Y) ? "scaleY" : Intrinsics.areEqual(property, DynamicAnimation.ROTATION) ? "rotation" : Intrinsics.areEqual(property, DynamicAnimation.ROTATION_X) ? "rotationX" : Intrinsics.areEqual(property, DynamicAnimation.ROTATION_Y) ? "rotationY" : Intrinsics.areEqual(property, DynamicAnimation.SCROLL_X) ? "scrollX" : Intrinsics.areEqual(property, DynamicAnimation.SCROLL_Y) ? "scrollY" : Intrinsics.areEqual(property, DynamicAnimation.ALPHA) ? "alpha" : "Custom FloatPropertyCompat instance";
        }
    }
}
