package com.incall.apps.navi.service.common.aidl.user.behavior.account;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;
import com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountCheckObserver;
import com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountLoginNoticeObserver;
import com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountLogoutObserver;
import com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountMobileLoginObserver;
import com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountQRCodeLoginAndBindObserver;
import com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountQRCodeLoginObserver;
import com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountRegisterObserver;
import com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountUnbindAndLogoutObserver;
import com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountVerificationCodeObserver;
import com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.ICarltdAccountStateObserver;
import com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.ICarltdCheckBindObserver;
import com.incall.apps.navi.service.common.data.returnbean.ReturnBeanForInt;
import com.incall.apps.navi.service.common.data.user.dataentry.AccountProfile;

/* loaded from: classes3.dex */
public interface IAccountBasicOperationService extends IInterface {

    public static class Default implements IAccountBasicOperationService {
        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
        public void addAccountLoginNoticeObserver(IAccountLoginNoticeObserver iAccountLoginNoticeObserver) throws RemoteException {
        }

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return null;
        }

        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
        public int checkAccountRegistered(String str, IAccountCheckObserver iAccountCheckObserver) throws RemoteException {
            return 0;
        }

        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
        public void destroy() throws RemoteException {
        }

        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
        public boolean getGdAccountBindState() throws RemoteException {
            return false;
        }

        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
        public AccountProfile getUserInfo() throws RemoteException {
            return null;
        }

        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
        public boolean getWechatBindState() throws RemoteException {
            return false;
        }

        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
        public void init() throws RemoteException {
        }

        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
        public boolean isLogin() throws RemoteException {
            return true;
        }

        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
        public int notifyCarltdAccountState(String str) throws RemoteException {
            return 0;
        }

        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
        public ReturnBeanForInt registerStateObserver(ICarltdAccountStateObserver iCarltdAccountStateObserver) throws RemoteException {
            return null;
        }

        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
        public void removeAccountLoginNoticeObserver(IAccountLoginNoticeObserver iAccountLoginNoticeObserver) throws RemoteException {
        }

        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
        public int requestCarltdCheckBind(ICarltdCheckBindObserver iCarltdCheckBindObserver) throws RemoteException {
            return 0;
        }

        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
        public int requestLoginVerificationCode(String str, IAccountVerificationCodeObserver iAccountVerificationCodeObserver) throws RemoteException {
            return 0;
        }

        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
        public int requestLogout(IAccountLogoutObserver iAccountLogoutObserver) throws RemoteException {
            return 0;
        }

        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
        public int requestMobileLogin(String str, String str2, IAccountMobileLoginObserver iAccountMobileLoginObserver) throws RemoteException {
            return 0;
        }

        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
        public int requestQRCode(IAccountQRCodeLoginObserver iAccountQRCodeLoginObserver) throws RemoteException {
            return 0;
        }

        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
        public int requestQRCodeForLoginAndBind(IAccountQRCodeLoginAndBindObserver iAccountQRCodeLoginAndBindObserver) throws RemoteException {
            return 0;
        }

        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
        public int requestRegister(String str, String str2, IAccountRegisterObserver iAccountRegisterObserver) throws RemoteException {
            return 0;
        }

        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
        public int requestRegisterVerificationCode(String str, IAccountVerificationCodeObserver iAccountVerificationCodeObserver) throws RemoteException {
            return 0;
        }

        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
        public int requestUnBindAndLogout(IAccountUnbindAndLogoutObserver iAccountUnbindAndLogoutObserver) throws RemoteException {
            return 0;
        }

        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
        public long requestUserInfo() throws RemoteException {
            return 0L;
        }

        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
        public int setSourceId(String str) throws RemoteException {
            return 0;
        }

        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
        public int unRegisterStateObserver(int i) throws RemoteException {
            return 0;
        }
    }

    void addAccountLoginNoticeObserver(IAccountLoginNoticeObserver iAccountLoginNoticeObserver) throws RemoteException;

    int checkAccountRegistered(String str, IAccountCheckObserver iAccountCheckObserver) throws RemoteException;

    void destroy() throws RemoteException;

    boolean getGdAccountBindState() throws RemoteException;

    AccountProfile getUserInfo() throws RemoteException;

    boolean getWechatBindState() throws RemoteException;

    void init() throws RemoteException;

    boolean isLogin() throws RemoteException;

    int notifyCarltdAccountState(String str) throws RemoteException;

    ReturnBeanForInt registerStateObserver(ICarltdAccountStateObserver iCarltdAccountStateObserver) throws RemoteException;

    void removeAccountLoginNoticeObserver(IAccountLoginNoticeObserver iAccountLoginNoticeObserver) throws RemoteException;

    int requestCarltdCheckBind(ICarltdCheckBindObserver iCarltdCheckBindObserver) throws RemoteException;

    int requestLoginVerificationCode(String str, IAccountVerificationCodeObserver iAccountVerificationCodeObserver) throws RemoteException;

    int requestLogout(IAccountLogoutObserver iAccountLogoutObserver) throws RemoteException;

    int requestMobileLogin(String str, String str2, IAccountMobileLoginObserver iAccountMobileLoginObserver) throws RemoteException;

    int requestQRCode(IAccountQRCodeLoginObserver iAccountQRCodeLoginObserver) throws RemoteException;

    int requestQRCodeForLoginAndBind(IAccountQRCodeLoginAndBindObserver iAccountQRCodeLoginAndBindObserver) throws RemoteException;

    int requestRegister(String str, String str2, IAccountRegisterObserver iAccountRegisterObserver) throws RemoteException;

    int requestRegisterVerificationCode(String str, IAccountVerificationCodeObserver iAccountVerificationCodeObserver) throws RemoteException;

    int requestUnBindAndLogout(IAccountUnbindAndLogoutObserver iAccountUnbindAndLogoutObserver) throws RemoteException;

    long requestUserInfo() throws RemoteException;

    int setSourceId(String str) throws RemoteException;

    int unRegisterStateObserver(int i) throws RemoteException;

    public static abstract class Stub extends Binder implements IAccountBasicOperationService {
        private static final String DESCRIPTOR = "com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService";
        static final int TRANSACTION_addAccountLoginNoticeObserver = 12;
        static final int TRANSACTION_checkAccountRegistered = 11;
        static final int TRANSACTION_destroy = 2;
        static final int TRANSACTION_getGdAccountBindState = 21;
        static final int TRANSACTION_getUserInfo = 6;
        static final int TRANSACTION_getWechatBindState = 22;
        static final int TRANSACTION_init = 1;
        static final int TRANSACTION_isLogin = 5;
        static final int TRANSACTION_notifyCarltdAccountState = 17;
        static final int TRANSACTION_registerStateObserver = 18;
        static final int TRANSACTION_removeAccountLoginNoticeObserver = 13;
        static final int TRANSACTION_requestCarltdCheckBind = 23;
        static final int TRANSACTION_requestLoginVerificationCode = 8;
        static final int TRANSACTION_requestLogout = 4;
        static final int TRANSACTION_requestMobileLogin = 9;
        static final int TRANSACTION_requestQRCode = 3;
        static final int TRANSACTION_requestQRCodeForLoginAndBind = 15;
        static final int TRANSACTION_requestRegister = 10;
        static final int TRANSACTION_requestRegisterVerificationCode = 7;
        static final int TRANSACTION_requestUnBindAndLogout = 16;
        static final int TRANSACTION_requestUserInfo = 14;
        static final int TRANSACTION_setSourceId = 20;
        static final int TRANSACTION_unRegisterStateObserver = 19;

        @Override // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        public Stub() {
            attachInterface(this, DESCRIPTOR);
        }

        public static IAccountBasicOperationService asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            if (queryLocalInterface != null && (queryLocalInterface instanceof IAccountBasicOperationService)) {
                return (IAccountBasicOperationService) queryLocalInterface;
            }
            return new Proxy(iBinder);
        }

        @Override // android.os.Binder
        public boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
            if (i == **********) {
                parcel2.writeString(DESCRIPTOR);
                return true;
            }
            switch (i) {
                case 1:
                    parcel.enforceInterface(DESCRIPTOR);
                    init();
                    parcel2.writeNoException();
                    return true;
                case 2:
                    parcel.enforceInterface(DESCRIPTOR);
                    destroy();
                    parcel2.writeNoException();
                    return true;
                case 3:
                    parcel.enforceInterface(DESCRIPTOR);
                    int requestQRCode = requestQRCode(IAccountQRCodeLoginObserver.Stub.asInterface(parcel.readStrongBinder()));
                    parcel2.writeNoException();
                    parcel2.writeInt(requestQRCode);
                    return true;
                case 4:
                    parcel.enforceInterface(DESCRIPTOR);
                    int requestLogout = requestLogout(IAccountLogoutObserver.Stub.asInterface(parcel.readStrongBinder()));
                    parcel2.writeNoException();
                    parcel2.writeInt(requestLogout);
                    return true;
                case 5:
                    parcel.enforceInterface(DESCRIPTOR);
                    boolean isLogin = isLogin();
                    parcel2.writeNoException();
                    parcel2.writeInt(isLogin ? 1 : 0);
                    return true;
                case 6:
                    parcel.enforceInterface(DESCRIPTOR);
                    AccountProfile userInfo = getUserInfo();
                    parcel2.writeNoException();
                    if (userInfo != null) {
                        parcel2.writeInt(1);
                        userInfo.writeToParcel(parcel2, 1);
                    } else {
                        parcel2.writeInt(0);
                    }
                    return true;
                case 7:
                    parcel.enforceInterface(DESCRIPTOR);
                    int requestRegisterVerificationCode = requestRegisterVerificationCode(parcel.readString(), IAccountVerificationCodeObserver.Stub.asInterface(parcel.readStrongBinder()));
                    parcel2.writeNoException();
                    parcel2.writeInt(requestRegisterVerificationCode);
                    return true;
                case 8:
                    parcel.enforceInterface(DESCRIPTOR);
                    int requestLoginVerificationCode = requestLoginVerificationCode(parcel.readString(), IAccountVerificationCodeObserver.Stub.asInterface(parcel.readStrongBinder()));
                    parcel2.writeNoException();
                    parcel2.writeInt(requestLoginVerificationCode);
                    return true;
                case 9:
                    parcel.enforceInterface(DESCRIPTOR);
                    int requestMobileLogin = requestMobileLogin(parcel.readString(), parcel.readString(), IAccountMobileLoginObserver.Stub.asInterface(parcel.readStrongBinder()));
                    parcel2.writeNoException();
                    parcel2.writeInt(requestMobileLogin);
                    return true;
                case 10:
                    parcel.enforceInterface(DESCRIPTOR);
                    int requestRegister = requestRegister(parcel.readString(), parcel.readString(), IAccountRegisterObserver.Stub.asInterface(parcel.readStrongBinder()));
                    parcel2.writeNoException();
                    parcel2.writeInt(requestRegister);
                    return true;
                case 11:
                    parcel.enforceInterface(DESCRIPTOR);
                    int checkAccountRegistered = checkAccountRegistered(parcel.readString(), IAccountCheckObserver.Stub.asInterface(parcel.readStrongBinder()));
                    parcel2.writeNoException();
                    parcel2.writeInt(checkAccountRegistered);
                    return true;
                case 12:
                    parcel.enforceInterface(DESCRIPTOR);
                    addAccountLoginNoticeObserver(IAccountLoginNoticeObserver.Stub.asInterface(parcel.readStrongBinder()));
                    parcel2.writeNoException();
                    return true;
                case 13:
                    parcel.enforceInterface(DESCRIPTOR);
                    removeAccountLoginNoticeObserver(IAccountLoginNoticeObserver.Stub.asInterface(parcel.readStrongBinder()));
                    parcel2.writeNoException();
                    return true;
                case 14:
                    parcel.enforceInterface(DESCRIPTOR);
                    long requestUserInfo = requestUserInfo();
                    parcel2.writeNoException();
                    parcel2.writeLong(requestUserInfo);
                    return true;
                case 15:
                    parcel.enforceInterface(DESCRIPTOR);
                    int requestQRCodeForLoginAndBind = requestQRCodeForLoginAndBind(IAccountQRCodeLoginAndBindObserver.Stub.asInterface(parcel.readStrongBinder()));
                    parcel2.writeNoException();
                    parcel2.writeInt(requestQRCodeForLoginAndBind);
                    return true;
                case 16:
                    parcel.enforceInterface(DESCRIPTOR);
                    int requestUnBindAndLogout = requestUnBindAndLogout(IAccountUnbindAndLogoutObserver.Stub.asInterface(parcel.readStrongBinder()));
                    parcel2.writeNoException();
                    parcel2.writeInt(requestUnBindAndLogout);
                    return true;
                case 17:
                    parcel.enforceInterface(DESCRIPTOR);
                    int notifyCarltdAccountState = notifyCarltdAccountState(parcel.readString());
                    parcel2.writeNoException();
                    parcel2.writeInt(notifyCarltdAccountState);
                    return true;
                case 18:
                    parcel.enforceInterface(DESCRIPTOR);
                    ReturnBeanForInt registerStateObserver = registerStateObserver(ICarltdAccountStateObserver.Stub.asInterface(parcel.readStrongBinder()));
                    parcel2.writeNoException();
                    if (registerStateObserver != null) {
                        parcel2.writeInt(1);
                        registerStateObserver.writeToParcel(parcel2, 1);
                    } else {
                        parcel2.writeInt(0);
                    }
                    return true;
                case 19:
                    parcel.enforceInterface(DESCRIPTOR);
                    int unRegisterStateObserver = unRegisterStateObserver(parcel.readInt());
                    parcel2.writeNoException();
                    parcel2.writeInt(unRegisterStateObserver);
                    return true;
                case 20:
                    parcel.enforceInterface(DESCRIPTOR);
                    int sourceId = setSourceId(parcel.readString());
                    parcel2.writeNoException();
                    parcel2.writeInt(sourceId);
                    return true;
                case 21:
                    parcel.enforceInterface(DESCRIPTOR);
                    boolean gdAccountBindState = getGdAccountBindState();
                    parcel2.writeNoException();
                    parcel2.writeInt(gdAccountBindState ? 1 : 0);
                    return true;
                case 22:
                    parcel.enforceInterface(DESCRIPTOR);
                    boolean wechatBindState = getWechatBindState();
                    parcel2.writeNoException();
                    parcel2.writeInt(wechatBindState ? 1 : 0);
                    return true;
                case 23:
                    parcel.enforceInterface(DESCRIPTOR);
                    int requestCarltdCheckBind = requestCarltdCheckBind(ICarltdCheckBindObserver.Stub.asInterface(parcel.readStrongBinder()));
                    parcel2.writeNoException();
                    parcel2.writeInt(requestCarltdCheckBind);
                    return true;
                default:
                    return super.onTransact(i, parcel, parcel2, i2);
            }
        }

        private static class Proxy implements IAccountBasicOperationService {
            public static IAccountBasicOperationService sDefaultImpl;
            private IBinder mRemote;

            public String getInterfaceDescriptor() {
                return Stub.DESCRIPTOR;
            }

            Proxy(IBinder iBinder) {
                this.mRemote = iBinder;
            }

            @Override // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
            public void init() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (!this.mRemote.transact(1, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().init();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
            public void destroy() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (!this.mRemote.transact(2, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().destroy();
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
            public int requestQRCode(IAccountQRCodeLoginObserver iAccountQRCodeLoginObserver) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeStrongBinder(iAccountQRCodeLoginObserver != null ? iAccountQRCodeLoginObserver.asBinder() : null);
                    if (!this.mRemote.transact(3, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().requestQRCode(iAccountQRCodeLoginObserver);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
            public int requestLogout(IAccountLogoutObserver iAccountLogoutObserver) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeStrongBinder(iAccountLogoutObserver != null ? iAccountLogoutObserver.asBinder() : null);
                    if (!this.mRemote.transact(4, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().requestLogout(iAccountLogoutObserver);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
            public boolean isLogin() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (!this.mRemote.transact(5, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().isLogin();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
            public AccountProfile getUserInfo() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (!this.mRemote.transact(6, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getUserInfo();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0 ? AccountProfile.CREATOR.createFromParcel(obtain2) : null;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
            public int requestRegisterVerificationCode(String str, IAccountVerificationCodeObserver iAccountVerificationCodeObserver) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeStrongBinder(iAccountVerificationCodeObserver != null ? iAccountVerificationCodeObserver.asBinder() : null);
                    if (!this.mRemote.transact(7, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().requestRegisterVerificationCode(str, iAccountVerificationCodeObserver);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
            public int requestLoginVerificationCode(String str, IAccountVerificationCodeObserver iAccountVerificationCodeObserver) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeStrongBinder(iAccountVerificationCodeObserver != null ? iAccountVerificationCodeObserver.asBinder() : null);
                    if (!this.mRemote.transact(8, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().requestLoginVerificationCode(str, iAccountVerificationCodeObserver);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
            public int requestMobileLogin(String str, String str2, IAccountMobileLoginObserver iAccountMobileLoginObserver) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeStrongBinder(iAccountMobileLoginObserver != null ? iAccountMobileLoginObserver.asBinder() : null);
                    if (!this.mRemote.transact(9, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().requestMobileLogin(str, str2, iAccountMobileLoginObserver);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
            public int requestRegister(String str, String str2, IAccountRegisterObserver iAccountRegisterObserver) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    obtain.writeStrongBinder(iAccountRegisterObserver != null ? iAccountRegisterObserver.asBinder() : null);
                    if (!this.mRemote.transact(10, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().requestRegister(str, str2, iAccountRegisterObserver);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
            public int checkAccountRegistered(String str, IAccountCheckObserver iAccountCheckObserver) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    obtain.writeStrongBinder(iAccountCheckObserver != null ? iAccountCheckObserver.asBinder() : null);
                    if (!this.mRemote.transact(11, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().checkAccountRegistered(str, iAccountCheckObserver);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
            public void addAccountLoginNoticeObserver(IAccountLoginNoticeObserver iAccountLoginNoticeObserver) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeStrongBinder(iAccountLoginNoticeObserver != null ? iAccountLoginNoticeObserver.asBinder() : null);
                    if (!this.mRemote.transact(12, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().addAccountLoginNoticeObserver(iAccountLoginNoticeObserver);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
            public void removeAccountLoginNoticeObserver(IAccountLoginNoticeObserver iAccountLoginNoticeObserver) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeStrongBinder(iAccountLoginNoticeObserver != null ? iAccountLoginNoticeObserver.asBinder() : null);
                    if (!this.mRemote.transact(13, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        Stub.getDefaultImpl().removeAccountLoginNoticeObserver(iAccountLoginNoticeObserver);
                    } else {
                        obtain2.readException();
                    }
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
            public long requestUserInfo() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (!this.mRemote.transact(14, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().requestUserInfo();
                    }
                    obtain2.readException();
                    return obtain2.readLong();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
            public int requestQRCodeForLoginAndBind(IAccountQRCodeLoginAndBindObserver iAccountQRCodeLoginAndBindObserver) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeStrongBinder(iAccountQRCodeLoginAndBindObserver != null ? iAccountQRCodeLoginAndBindObserver.asBinder() : null);
                    if (!this.mRemote.transact(15, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().requestQRCodeForLoginAndBind(iAccountQRCodeLoginAndBindObserver);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
            public int requestUnBindAndLogout(IAccountUnbindAndLogoutObserver iAccountUnbindAndLogoutObserver) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeStrongBinder(iAccountUnbindAndLogoutObserver != null ? iAccountUnbindAndLogoutObserver.asBinder() : null);
                    if (!this.mRemote.transact(16, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().requestUnBindAndLogout(iAccountUnbindAndLogoutObserver);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
            public int notifyCarltdAccountState(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    if (!this.mRemote.transact(17, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().notifyCarltdAccountState(str);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
            public ReturnBeanForInt registerStateObserver(ICarltdAccountStateObserver iCarltdAccountStateObserver) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeStrongBinder(iCarltdAccountStateObserver != null ? iCarltdAccountStateObserver.asBinder() : null);
                    if (!this.mRemote.transact(18, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().registerStateObserver(iCarltdAccountStateObserver);
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0 ? ReturnBeanForInt.CREATOR.createFromParcel(obtain2) : null;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
            public int unRegisterStateObserver(int i) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeInt(i);
                    if (!this.mRemote.transact(19, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().unRegisterStateObserver(i);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
            public int setSourceId(String str) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeString(str);
                    if (!this.mRemote.transact(20, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().setSourceId(str);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
            public boolean getGdAccountBindState() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (!this.mRemote.transact(21, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getGdAccountBindState();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
            public boolean getWechatBindState() throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    if (!this.mRemote.transact(22, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().getWechatBindState();
                    }
                    obtain2.readException();
                    return obtain2.readInt() != 0;
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService
            public int requestCarltdCheckBind(ICarltdCheckBindObserver iCarltdCheckBindObserver) throws RemoteException {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken(Stub.DESCRIPTOR);
                    obtain.writeStrongBinder(iCarltdCheckBindObserver != null ? iCarltdCheckBindObserver.asBinder() : null);
                    if (!this.mRemote.transact(23, obtain, obtain2, 0) && Stub.getDefaultImpl() != null) {
                        return Stub.getDefaultImpl().requestCarltdCheckBind(iCarltdCheckBindObserver);
                    }
                    obtain2.readException();
                    return obtain2.readInt();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }

        public static boolean setDefaultImpl(IAccountBasicOperationService iAccountBasicOperationService) {
            if (Proxy.sDefaultImpl != null) {
                throw new IllegalStateException("setDefaultImpl() called twice");
            }
            if (iAccountBasicOperationService == null) {
                return false;
            }
            Proxy.sDefaultImpl = iAccountBasicOperationService;
            return true;
        }

        public static IAccountBasicOperationService getDefaultImpl() {
            return Proxy.sDefaultImpl;
        }
    }
}
