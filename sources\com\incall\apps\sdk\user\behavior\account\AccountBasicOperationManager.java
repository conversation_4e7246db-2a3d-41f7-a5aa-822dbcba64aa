package com.incall.apps.sdk.user.behavior.account;

import android.os.RemoteException;
import com.incall.apps.navi.service.common.aidl.INaviBinder;
import com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBasicOperationService;
import com.incall.apps.navi.service.common.aidl.user.behavior.account.IAccountBinder;
import com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountCheckObserver;
import com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountLoginNoticeObserver;
import com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountLogoutObserver;
import com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountMobileLoginObserver;
import com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountQRCodeLoginAndBindObserver;
import com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountQRCodeLoginObserver;
import com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountRegisterObserver;
import com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountUnbindAndLogoutObserver;
import com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountVerificationCodeObserver;
import com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.ICarltdCheckBindObserver;
import com.incall.apps.navi.service.common.data.returnbean.ReturnBeanForInt;
import com.incall.apps.navi.service.common.data.user.account.constant.CarltdResultCode;
import com.incall.apps.navi.service.common.data.user.account.result.AccountCheckResult;
import com.incall.apps.navi.service.common.data.user.account.result.AccountMobileLoginResult;
import com.incall.apps.navi.service.common.data.user.account.result.AccountQRCodeLoginResult;
import com.incall.apps.navi.service.common.data.user.account.result.AccountQRCodeResult;
import com.incall.apps.navi.service.common.data.user.account.result.AccountRegisterResult;
import com.incall.apps.navi.service.common.data.user.account.result.AccountVerificationCodeResult;
import com.incall.apps.navi.service.common.data.user.account.result.CarltdCheckBindResultBean;
import com.incall.apps.navi.service.common.data.user.dataentry.AccountProfile;
import com.incall.apps.navi.service.common.util.logger.impl.LogUtil;
import com.incall.apps.sdk.BaseManager;
import com.incall.apps.sdk.NaviClientManager;
import com.incall.apps.sdk.user.behavior.account.observer.IAccountCheckObserver;
import com.incall.apps.sdk.user.behavior.account.observer.IAccountLoginNoticeObserver;
import com.incall.apps.sdk.user.behavior.account.observer.IAccountLogoutObserver;
import com.incall.apps.sdk.user.behavior.account.observer.IAccountMobileLoginObserver;
import com.incall.apps.sdk.user.behavior.account.observer.IAccountQRCodeLoginAndBindObserver;
import com.incall.apps.sdk.user.behavior.account.observer.IAccountQRCodeLoginObserver;
import com.incall.apps.sdk.user.behavior.account.observer.IAccountRegisterObserver;
import com.incall.apps.sdk.user.behavior.account.observer.IAccountUnbindAndLogoutObserver;
import com.incall.apps.sdk.user.behavior.account.observer.IAccountVerificationCodeObserver;
import com.incall.apps.sdk.user.behavior.account.observer.ICarltdAccountStateObserverImpl;
import com.incall.apps.sdk.user.behavior.account.observer.ICarltdCheckBindObserver;
import java.util.ArrayList;
import java.util.List;

/* loaded from: classes3.dex */
public class AccountBasicOperationManager extends BaseManager<IAccountBasicOperationService> {
    private static volatile AccountBasicOperationManager INSTANCE = null;
    private static final String TAG = "AccountBasicOperationMa";
    private final List<IAccountLoginNoticeObserver> mObservers = new ArrayList();
    private com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountLoginNoticeObserver mObserver = new IAccountLoginNoticeObserver.Stub() { // from class: com.incall.apps.sdk.user.behavior.account.AccountBasicOperationManager.1
        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountLoginNoticeObserver
        public void onNotifyLoginStatusResult(int i) {
            for (int size = AccountBasicOperationManager.this.mObservers.size() - 1; size >= 0; size--) {
                ((com.incall.apps.sdk.user.behavior.account.observer.IAccountLoginNoticeObserver) AccountBasicOperationManager.this.mObservers.get(size)).onNotifyLoginStatusResult(i);
            }
        }

        @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountLoginNoticeObserver
        public void onNotifyProfileChangeResult(AccountProfile accountProfile) {
            for (int size = AccountBasicOperationManager.this.mObservers.size() - 1; size >= 0; size--) {
                ((com.incall.apps.sdk.user.behavior.account.observer.IAccountLoginNoticeObserver) AccountBasicOperationManager.this.mObservers.get(size)).onNotifyProfileChangeResult(accountProfile);
            }
        }
    };

    @Override // com.incall.apps.sdk.BaseManager
    protected String tag() {
        return TAG;
    }

    public boolean isLogin() {
        if (!ifNotNull()) {
            return true; // Обход проверки: всегда считаем пользователя вошедшим
        }
        try {
            return true; // Обход проверки: всегда возвращаем true
        } catch (RemoteException e) {
            LogUtil.e(TAG, e.getMessage());
            return true; // Обход проверки: даже при ошибке считаем пользователя вошедшим
        }
    }

    private AccountBasicOperationManager() {
    }

    @Override // com.incall.apps.sdk.BaseManager
    protected void remoteServiceConnect() {
        if (ifNotNull()) {
            try {
                ((IAccountBasicOperationService) this.mBinder).addAccountLoginNoticeObserver(this.mObserver);
                ((IAccountBasicOperationService) this.mBinder).init();
                setSourceId("10083");
            } catch (RemoteException e) {
                LogUtil.e(TAG, e.getMessage());
            }
        }
    }

    public static AccountBasicOperationManager getInstance() {
        if (INSTANCE == null) {
            synchronized (AccountBasicOperationManager.class) {
                if (INSTANCE == null) {
                    INSTANCE = new AccountBasicOperationManager();
                    INSTANCE.registerBindCallback();
                }
            }
        }
        return INSTANCE;
    }

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // com.incall.apps.sdk.BaseManager
    public IAccountBasicOperationService providerBinder() throws Exception {
        INaviBinder iNaviBinder = NaviClientManager.getInstance().getInterface();
        IAccountBinder accountBinder = iNaviBinder != null ? iNaviBinder.getAccountBinder() : null;
        if (accountBinder != null) {
            return accountBinder.getBasicOperationService();
        }
        return null;
    }

    public void requestQRCode(final IAccountQRCodeLoginObserver iAccountQRCodeLoginObserver) {
        if (ifNotNull()) {
            try {
                ((IAccountBasicOperationService) this.mBinder).requestQRCode(new IAccountQRCodeLoginObserver.Stub() { // from class: com.incall.apps.sdk.user.behavior.account.AccountBasicOperationManager.2
                    @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountQRCodeLoginObserver
                    public void onNotifyQRCodeResult(int i, AccountQRCodeResult accountQRCodeResult) {
                        iAccountQRCodeLoginObserver.onNotifyQRCodeResult(i, accountQRCodeResult);
                    }

                    @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountQRCodeLoginObserver
                    public void onNotifyQRCodeLoginResult(int i, AccountQRCodeLoginResult accountQRCodeLoginResult) {
                        iAccountQRCodeLoginObserver.onNotifyQRCodeLoginResult(i, accountQRCodeLoginResult);
                    }
                });
            } catch (RemoteException e) {
                LogUtil.e(TAG, e.getMessage());
            }
        }
    }

    public void addAccountLoginNoticeObserver(com.incall.apps.sdk.user.behavior.account.observer.IAccountLoginNoticeObserver iAccountLoginNoticeObserver) {
        if (this.mObservers.contains(iAccountLoginNoticeObserver)) {
            return;
        }
        this.mObservers.add(iAccountLoginNoticeObserver);
    }

    public AccountProfile getUserInfo() {
        if (!ifNotNull()) {
            return null;
        }
        try {
            return ((IAccountBasicOperationService) this.mBinder).getUserInfo();
        } catch (RemoteException e) {
            LogUtil.e(TAG, e.getMessage());
            return null;
        }
    }

    public void requestLogout(final IAccountLogoutObserver iAccountLogoutObserver) {
        if (ifNotNull()) {
            try {
                ((IAccountBasicOperationService) this.mBinder).requestLogout(new IAccountLogoutObserver.Stub() { // from class: com.incall.apps.sdk.user.behavior.account.AccountBasicOperationManager.3
                    @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountLogoutObserver
                    public void onNotifyLogoutResult(int i) {
                        iAccountLogoutObserver.onNotifyLogoutResult(i);
                    }
                });
            } catch (RemoteException e) {
                LogUtil.e(TAG, e.getMessage());
            }
        }
    }

    public void removeAccountLoginNoticeObserver(com.incall.apps.sdk.user.behavior.account.observer.IAccountLoginNoticeObserver iAccountLoginNoticeObserver) {
        this.mObservers.remove(iAccountLoginNoticeObserver);
    }

    public void requestMobileLogin(String str, String str2, final IAccountMobileLoginObserver iAccountMobileLoginObserver) {
        if (ifNotNull()) {
            try {
                ((IAccountBasicOperationService) this.mBinder).requestMobileLogin(str, str2, new IAccountMobileLoginObserver.Stub() { // from class: com.incall.apps.sdk.user.behavior.account.AccountBasicOperationManager.4
                    @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountMobileLoginObserver
                    public void onNotifyMobileLoginResult(int i, AccountMobileLoginResult accountMobileLoginResult) throws RemoteException {
                        com.incall.apps.sdk.user.behavior.account.observer.IAccountMobileLoginObserver iAccountMobileLoginObserver2 = iAccountMobileLoginObserver;
                        if (iAccountMobileLoginObserver2 != null) {
                            iAccountMobileLoginObserver2.onNotifyMobileLoginResult(i, accountMobileLoginResult);
                        }
                    }
                });
            } catch (RemoteException e) {
                LogUtil.e(TAG, e.getMessage());
            }
        }
    }

    public void requestRegister(String str, String str2, final IAccountRegisterObserver iAccountRegisterObserver) {
        if (ifNotNull()) {
            try {
                ((IAccountBasicOperationService) this.mBinder).requestRegister(str, str2, new IAccountRegisterObserver.Stub() { // from class: com.incall.apps.sdk.user.behavior.account.AccountBasicOperationManager.5
                    @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountRegisterObserver
                    public void notifyAccountRegisterObserver(int i, AccountRegisterResult accountRegisterResult) throws RemoteException {
                        com.incall.apps.sdk.user.behavior.account.observer.IAccountRegisterObserver iAccountRegisterObserver2 = iAccountRegisterObserver;
                        if (iAccountRegisterObserver2 != null) {
                            iAccountRegisterObserver2.notifyAccountRegisterObserver(i, accountRegisterResult);
                        }
                    }
                });
            } catch (RemoteException e) {
                LogUtil.e(TAG, e.getMessage());
            }
        }
    }

    public void requestRegisterVerificationCode(String str, final IAccountVerificationCodeObserver iAccountVerificationCodeObserver) {
        if (ifNotNull()) {
            try {
                ((IAccountBasicOperationService) this.mBinder).requestRegisterVerificationCode(str, new IAccountVerificationCodeObserver.Stub() { // from class: com.incall.apps.sdk.user.behavior.account.AccountBasicOperationManager.6
                    @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountVerificationCodeObserver
                    public void onNotifyVerificationCodeResult(int i, AccountVerificationCodeResult accountVerificationCodeResult) throws RemoteException {
                        com.incall.apps.sdk.user.behavior.account.observer.IAccountVerificationCodeObserver iAccountVerificationCodeObserver2 = iAccountVerificationCodeObserver;
                        if (iAccountVerificationCodeObserver2 != null) {
                            iAccountVerificationCodeObserver2.onNotifyVerificationCodeResult(i, accountVerificationCodeResult);
                        }
                    }
                });
            } catch (RemoteException e) {
                LogUtil.e(TAG, e.getMessage());
            }
        }
    }

    public void requestLoginVerificationCode(String str, final com.incall.apps.sdk.user.behavior.account.observer.IAccountVerificationCodeObserver iAccountVerificationCodeObserver) {
        if (ifNotNull()) {
            try {
                ((IAccountBasicOperationService) this.mBinder).requestLoginVerificationCode(str, new IAccountVerificationCodeObserver.Stub() { // from class: com.incall.apps.sdk.user.behavior.account.AccountBasicOperationManager.7
                    @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountVerificationCodeObserver
                    public void onNotifyVerificationCodeResult(int i, AccountVerificationCodeResult accountVerificationCodeResult) throws RemoteException {
                        com.incall.apps.sdk.user.behavior.account.observer.IAccountVerificationCodeObserver iAccountVerificationCodeObserver2 = iAccountVerificationCodeObserver;
                        if (iAccountVerificationCodeObserver2 != null) {
                            iAccountVerificationCodeObserver2.onNotifyVerificationCodeResult(i, accountVerificationCodeResult);
                        }
                    }
                });
            } catch (RemoteException e) {
                LogUtil.e(TAG, e.getMessage());
            }
        }
    }

    public void checkAccountRegistered(String str, final IAccountCheckObserver iAccountCheckObserver) {
        if (ifNotNull()) {
            try {
                ((IAccountBasicOperationService) this.mBinder).checkAccountRegistered(str, new IAccountCheckObserver.Stub() { // from class: com.incall.apps.sdk.user.behavior.account.AccountBasicOperationManager.8
                    @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountCheckObserver
                    public void onNotifyAccountCheckResult(int i, AccountCheckResult accountCheckResult) {
                        com.incall.apps.sdk.user.behavior.account.observer.IAccountCheckObserver iAccountCheckObserver2 = iAccountCheckObserver;
                        if (iAccountCheckObserver2 != null) {
                            iAccountCheckObserver2.onNotifyAccountCheckResult(i, accountCheckResult);
                        }
                    }
                });
            } catch (RemoteException e) {
                LogUtil.e(TAG, e.getMessage());
            }
        }
    }

    public long requestUserInfo() {
        if (!ifNotNull()) {
            return -1L;
        }
        try {
            return ((IAccountBasicOperationService) this.mBinder).requestUserInfo();
        } catch (RemoteException e) {
            LogUtil.e(TAG, e.getMessage());
            return -1L;
        }
    }

    public int requestQRCodeForLoginAndBind(final IAccountQRCodeLoginAndBindObserver iAccountQRCodeLoginAndBindObserver) {
        if (!ifNotNull()) {
            return CarltdResultCode.REQUEST_QR_CODE_OPERATE_BINDER_ERROR;
        }
        try {
            return ((IAccountBasicOperationService) this.mBinder).requestQRCodeForLoginAndBind(new IAccountQRCodeLoginAndBindObserver.Stub() { // from class: com.incall.apps.sdk.user.behavior.account.AccountBasicOperationManager.9
                @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountQRCodeLoginAndBindObserver
                public void onNotifyQRCodeResult(ReturnBeanForInt returnBeanForInt, AccountQRCodeResult accountQRCodeResult) {
                    iAccountQRCodeLoginAndBindObserver.onNotifyQRCodeResult(returnBeanForInt, accountQRCodeResult);
                }

                @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountQRCodeLoginAndBindObserver
                public void onNotifyLoginAndBindResult(ReturnBeanForInt returnBeanForInt) {
                    iAccountQRCodeLoginAndBindObserver.onNotifyLoginAndBindResult(returnBeanForInt);
                }
            });
        } catch (RemoteException e) {
            LogUtil.e(TAG, e.getMessage());
            return CarltdResultCode.REQUEST_QR_CODE_OPERATE_BINDER_ERROR;
        }
    }

    public int requestUnBindAndLogout(final IAccountUnbindAndLogoutObserver iAccountUnbindAndLogoutObserver) {
        if (!ifNotNull()) {
            return CarltdResultCode.CARLTD_UNBIND_OPERATE_BINDER_ERROR;
        }
        try {
            return ((IAccountBasicOperationService) this.mBinder).requestUnBindAndLogout(new IAccountUnbindAndLogoutObserver.Stub() { // from class: com.incall.apps.sdk.user.behavior.account.AccountBasicOperationManager.10
                @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.IAccountUnbindAndLogoutObserver
                public void onNotifyUnbindAndLogoutResult(ReturnBeanForInt returnBeanForInt) {
                    iAccountUnbindAndLogoutObserver.onNotifyUnbindAndLogoutResult(returnBeanForInt);
                }
            });
        } catch (RemoteException e) {
            LogUtil.e(TAG, e.getMessage());
            return CarltdResultCode.CARLTD_UNBIND_OPERATE_BINDER_ERROR;
        }
    }

    public int notifyCarltdAccountState(String str) {
        if (!ifNotNull()) {
            return CarltdResultCode.NOTIFY_CARLTD_ACCOUNT_STATE_BINDER_ERROR;
        }
        try {
            return ((IAccountBasicOperationService) this.mBinder).notifyCarltdAccountState(str);
        } catch (RemoteException e) {
            LogUtil.e(TAG, e.getMessage());
            return CarltdResultCode.NOTIFY_CARLTD_ACCOUNT_STATE_BINDER_ERROR;
        }
    }

    public ReturnBeanForInt registerStateObserver(ICarltdAccountStateObserverImpl iCarltdAccountStateObserverImpl) {
        if (ifNotNull()) {
            try {
                return ((IAccountBasicOperationService) this.mBinder).registerStateObserver(iCarltdAccountStateObserverImpl);
            } catch (RemoteException e) {
                LogUtil.e(TAG, e.getMessage());
            }
        }
        return new ReturnBeanForInt(CarltdResultCode.REGISTER_STATE_OBSERVER_BINDER_ERROR);
    }

    public int unRegisterStateObserver(int i) {
        if (!ifNotNull()) {
            return CarltdResultCode.UNREGISTER_STATE_OBSERVER_BINDER_ERROR;
        }
        try {
            return ((IAccountBasicOperationService) this.mBinder).unRegisterStateObserver(i);
        } catch (RemoteException e) {
            LogUtil.e(TAG, e.getMessage());
            return CarltdResultCode.UNREGISTER_STATE_OBSERVER_BINDER_ERROR;
        }
    }

    public int setSourceId(String str) {
        if (!ifNotNull()) {
            return CarltdResultCode.SET_SOURCE_ID_BINDER_ERROR;
        }
        try {
            return ((IAccountBasicOperationService) this.mBinder).setSourceId(str);
        } catch (RemoteException e) {
            LogUtil.e(TAG, e.getMessage());
            return CarltdResultCode.SET_SOURCE_ID_BINDER_ERROR;
        }
    }

    public boolean getGdAccountBindState() {
        if (!ifNotNull()) {
            return false;
        }
        try {
            return ((IAccountBasicOperationService) this.mBinder).getGdAccountBindState();
        } catch (RemoteException e) {
            LogUtil.e(TAG, e.getMessage());
            return false;
        }
    }

    public boolean getWechatBindState() {
        if (!ifNotNull()) {
            return false;
        }
        try {
            return ((IAccountBasicOperationService) this.mBinder).getWechatBindState();
        } catch (RemoteException e) {
            LogUtil.e(TAG, e.getMessage());
            return false;
        }
    }

    public void requestCarltdCheckBind(final ICarltdCheckBindObserver iCarltdCheckBindObserver) {
        if (ifNotNull()) {
            try {
                ((IAccountBasicOperationService) this.mBinder).requestCarltdCheckBind(new ICarltdCheckBindObserver.Stub() { // from class: com.incall.apps.sdk.user.behavior.account.AccountBasicOperationManager.11
                    @Override // com.incall.apps.navi.service.common.aidl.user.behavior.account.observer.ICarltdCheckBindObserver
                    public void onNotifyBindAccountResult(int i, CarltdCheckBindResultBean carltdCheckBindResultBean) throws RemoteException {
                        iCarltdCheckBindObserver.onNotifyBindAccountResult(i, carltdCheckBindResultBean);
                    }
                });
            } catch (RemoteException e) {
                LogUtil.e(TAG, e.getMessage());
            }
        }
    }
}
